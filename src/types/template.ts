import {TemplateFormStatus, TemplateStatus} from '../enums';
import {BasicUserDetails} from './user';

export interface DefaultAPIFields {
  updated_at?: string;
  updated_by?: string;
  created_at?: string;
  created_by?: string;
}
export interface TemplateFormCategory {
  category_id: number[];
  is_other: boolean;
  value: string;
}
export interface Parameter {
  is_other: boolean;
  parameter_type_id: number;
  parameter_id: number[];
  value: string;
}
export interface TemplateFormHazard {
  is_other: boolean;
  value: string;
  hazard_id: number[];
}

export interface TemplateFormJobInitialRiskRating {
  parameter_type_id: number | null;
  rating: string;
}

export interface TemplateFormJobResidualRiskRating {
  parameter_type_id: number | null;
  rating: string;
  reason: string;
}
export interface TemplateFormJob {
  job_id: string;
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_existing_control: string;
  job_additional_mitigation: string;
  job_close_out_date: string;
  job_close_out_responsibility_id: string;
  job_close_out_responsibility_label: string;
  template_job_initial_risk_rating: TemplateFormJobInitialRiskRating[];
  template_job_residual_risk_rating: TemplateFormJobResidualRiskRating[];
}

export interface TemplateFormTaskReliabilityAssessment {
  task_reliability_assessment_id: number | null;
  task_reliability_assessment_answer: string;
  condition: string;
}

export interface TemplateForm extends DefaultAPIFields {
  id?: number;
  task_requiring_ra: string;
  task_duration: string;
  task_alternative_consideration: string;
  task_rejection_reason: string;
  worst_case_scenario: string;
  recovery_measures: string;
  status: TemplateFormStatus;
  template_category: TemplateFormCategory;
  template_hazard: TemplateFormHazard;
  parameters: Parameter[];
  template_job: TemplateFormJob[];
  template_task_reliability_assessment: TemplateFormTaskReliabilityAssessment[];
  template_keyword: string[];
  draft_step?: number; // Uncomment if needed
  ra_level?: number;
}

export interface Category {
  id: number;
  name: string;
  type: number;
}

export interface TemplateCategory {
  id: number;
  template_id: number;
  category_id: number;
  category_is_other: boolean;
  status: number;
  category: Category | null;
  value: string | null;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface HazardDetail {
  id: number;
  name: string;
  type: number;
}

export interface TemplateHazard {
  id: number;
  template_id: number;
  hazard_id: number;
  hazard_category_is_other: boolean;
  status: number;
  value: string | null;
  hazard_detail: HazardDetail | null;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface RiskRating {
  id: number;
  template_job_id: number;
  parameter_type_id?: number;
  parameter_type_i?: number;
  rating: string;
  reason?: string;
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateJob {
  id: number;
  template_id: number;
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_existing_control: string;
  job_additional_mitigation: string;
  job_close_out_date: string;
  job_close_out_responsibility_id: string;
  job_close_out_responsibility_label: string;
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
  template_job_initial_risk_rating: RiskRating[];
  template_job_residual_risk_rating: RiskRating[];
}

export interface TemplateReliabilityAssessment {
  id: number;
  template_id: number;
  task_reliability_assessmen: string;
  condition: string;
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateKeyword {
  id: number;
  template_id: number;
  name: string;
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Template {
  id: number;
  task_requiring_ra: string;
  task_duration: string;
  task_alternative_consideration: string;
  task_rejection_reason: string;
  worst_case_scenario: string;
  recovery_measures: string;
  status: TemplateStatus;
  created_by: string;
  updated_by: string;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
  created_at: string;
  updated_at: string;
  template_category: TemplateCategory[];
  template_hazards: TemplateHazard[];
  template_job: TemplateJob[];
  template_task_reliability_assessment: TemplateReliabilityAssessment[];
  template_keywords: TemplateKeyword[];
}

export interface TemplateListResponse {
  message: string;
  result: {
    data: Template[];
    pagination: {
      totalItems: number;
      totalPages: number;
      page: number;
      pageSize: number;
    };
    userDetails: BasicUserDetails[];
  };
}

export interface TemplateUserResponse {
  message: string;
  result: BasicUserDetails[];
}

export type TopTemplate = Pick<
  Template,
  | 'id'
  | 'task_requiring_ra'
  | 'created_by'
  | 'created_at'
  | 'template_category'
  | 'template_hazards'
  | 'template_keywords'
>;

export interface MostlyUsedTemplateResponse {
  message: string;
  result: {
    results: TopTemplate[];
    userDetails: BasicUserDetails[];
  };
}
