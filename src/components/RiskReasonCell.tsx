import React from 'react';

type RiskRating = {
  parameter_type_id: number;
  reason?: string;
};

type CellRendererProps = {
  rating: RiskRating[];
};

export const RiskReasonCell: React.FC<CellRendererProps> = ({rating}) => {
  const ratingMap = new Map(rating.map(r => [r.parameter_type_id, r]));

  return (
    <div className="d-flex flex-column">
      {[1, 2, 3, 4].map((paramId, index) => {
        const reason = ratingMap.get(paramId)?.reason?.trim() || '---';

        return (
          <div key={paramId}>
            <div className="d-flex align-items-start gap-2 mb-2">
              <span className="fs-14">{reason}</span>
            </div>
            {index < 3 && <hr className="hr-rem" />}
          </div>
        );
      })}
    </div>
  );
};
