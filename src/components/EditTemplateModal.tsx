import React, {useRef, useState, useEffect} from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';
import {cloneDeep, isEqual} from 'lodash';
import {TemplateForm} from '../types/template';
import {RiskForm} from '../types/risk';
import {RaCategoryStep} from '../pages/CreateRA/RaCategoryStep';
import {HazardCategoryStep} from '../pages/CreateRA/HazardCategoryStep';
import {AtRiskStep} from '../pages/CreateRA/AtRiskStep';
import {AddJobsStep} from '../pages/CreateRA/AddJobsStep';
import EditBasicDetailsComp from './EditBasicDetail';
import {AddTeamMembersStep} from '../pages/CreateRA/AddTeamMembersStep';

type Props = {
  onClose: (
    shouldSave?: boolean,
    updatedForm?: TemplateForm | RiskForm,
  ) => void;
  title: string;
  step: number;
  form: TemplateForm | RiskForm;
  jobId?: string;
  type?: 'template' | 'risk';
};

export const EditTemplateModal: React.FC<Props> = ({
  onClose,
  title,
  step,
  form,
  jobId,
  type = 'template',
}) => {
  const [jobIndex, setJobIndex] = useState(0);

  const raCategoryRef = useRef<any>(null);
  const hazardCategoryRef = useRef<any>(null);
  const atRiskRef = useRef<any>(null);
  const addJobsRef = useRef<any>(null);
  const addTeamMembersRef = useRef<any>(null);

  // Helper function to check if form is a risk form
  const isRiskForm = (form: TemplateForm | RiskForm): form is RiskForm => {
    return type === 'risk' || 'risk_job' in form;
  };

  /**
   * Validates the risk form basic details (step 1).
   * Checks all required fields for risk assessment creation.
   *
   * @param {RiskForm} riskForm - The risk form to validate
   * @returns {boolean} True if all required fields are valid, false otherwise
   */
  const validateRiskFormBasicDetails = (riskForm: RiskForm): boolean => {
    const isVesselOrOfficeSelected =
      !!riskForm.vessel_ownership_id || !!riskForm.office_id;

    return (
      riskForm.task_requiring_ra.trim() !== '' &&
      riskForm.task_duration.trim() !== '' &&
      !!riskForm.assessor &&
      riskForm.assessor !== 0 &&
      isVesselOrOfficeSelected &&
      !!riskForm.date_risk_assessment &&
      riskForm.date_risk_assessment.trim() !== '' &&
      !!riskForm.approval_required &&
      riskForm.approval_required.length > 0
    );
  };

  // Create a cloned form for editing
  const [clonedForm, setClonedForm] = useState<TemplateForm | RiskForm>(() => {
    const cloned = cloneDeep(form);

    // For step 5 (AddJobsStep), filter to keep only the job with matching jobId
    if (step === 5 && jobId) {
      if (isRiskForm(cloned)) {
        // For risk forms, jobId is the index as string
        const jobIndex = parseInt(jobId, 10);
        const riskJobs = cloned.risk_job || [];
        const filteredJob = riskJobs[jobIndex];
        cloned.risk_job = filteredJob ? [filteredJob] : [];
      } else {
        // For template forms, jobId is the actual job_id
        const filteredJob = cloned.template_job.find(
          job => job.job_id === jobId,
        );
        cloned.template_job = filteredJob ? [filteredJob] : [];
      }
    }

    return cloned;
  });
  const [isFormValid, setIsFormValid] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (!jobId) return;

    if (isRiskForm(form)) {
      // For risk forms, jobId is already the index as string
      const index = parseInt(jobId, 10);
      if (!isNaN(index) && (form.risk_job?.length || 0) > index) {
        setJobIndex(index);
      }
      return; // Exit early if it's a risk form
    }

    // For template forms, find the index by job_id
    if (form.template_job?.length > 0) {
      const index = form.template_job.findIndex(job => job.job_id === jobId);
      if (index !== -1) {
        setJobIndex(index);
      }
    }
  }, [jobId, form, type]);

  // Helper function to check changes for risk forms in step 5
  const checkRiskFormChanges = (
    jobId: string,
    form: RiskForm,
    clonedForm: RiskForm,
  ): boolean => {
    const jobIndex = parseInt(jobId, 10);
    const originalJobs = form.risk_job || [];
    const clonedJobs = clonedForm.risk_job || [];

    if (!isNaN(jobIndex) && originalJobs[jobIndex] && clonedJobs.length > 0) {
      const originalJob = originalJobs[jobIndex];
      const editedJob = clonedJobs[0];
      return !isEqual(originalJob, editedJob);
    }
    return false;
  };

  // Helper function to check changes for template forms in step 5
  const checkTemplateFormChanges = (
    jobId: string,
    form: TemplateForm,
    clonedForm: TemplateForm,
  ): boolean => {
    if (clonedForm.template_job.length > 0) {
      const originalJob = form.template_job.find(job => job.job_id === jobId);
      const editedJob = clonedForm.template_job[0];
      return !isEqual(originalJob, editedJob);
    }
    return false;
  };

  // Check for changes whenever clonedForm updates
  useEffect(() => {
    let formHasChanges = false;

    if (step === 5 && jobId) {
      if (isRiskForm(form) && isRiskForm(clonedForm)) {
        formHasChanges = checkRiskFormChanges(jobId, form, clonedForm);
      } else {
        formHasChanges = checkTemplateFormChanges(
          jobId,
          form as TemplateForm,
          clonedForm as TemplateForm,
        );
      }
    } else {
      // For other steps, compare the entire form
      formHasChanges = !isEqual(form, clonedForm);
    }

    setHasChanges(formHasChanges);
  }, [form, clonedForm, step, jobId, type]);

  // Validation for step 1
  useEffect(() => {
    if (step === 1) {
      let isStep1Valid = false;

      if (type === 'risk') {
        const riskForm = clonedForm as RiskForm;
        isStep1Valid = validateRiskFormBasicDetails(riskForm);
      } else {
        // Template form validation (existing logic)
        isStep1Valid =
          clonedForm.task_requiring_ra.trim() !== '' &&
          clonedForm.task_duration.trim() !== '';
      }
      setIsFormValid(isStep1Valid);
    }
  }, [clonedForm, step, type]);

  // Handle validation from child components
  const handleValidation = (valid: boolean) => {
    setIsFormValid(valid);
  };

  const validateStep = () => {
    if (step === 1) {
      if (type === 'risk') {
        const riskForm = clonedForm as RiskForm;
        return validateRiskFormBasicDetails(riskForm);
      }
      return (
        clonedForm.task_requiring_ra.trim() !== '' &&
        clonedForm.task_duration.trim() !== ''
      );
    }
    if (step === 2 && raCategoryRef.current)
      return raCategoryRef.current.validate();
    if (step === 3 && hazardCategoryRef.current)
      return hazardCategoryRef.current.validate();
    if (step === 4 && atRiskRef.current) return atRiskRef.current.validate();
    if (step === 5 && addJobsRef.current) return addJobsRef.current.validate();
    if (step === 6 && addTeamMembersRef.current)
      return addTeamMembersRef.current.validate();
    return false;
  };

  const handleSave = async () => {
    const isValid = validateStep();
    if (!isValid) {
      return;
    }

    let updatedForm = cloneDeep(clonedForm);

    if (step === 5 && jobId) {
      // Check if there are jobs to save
      const hasJobs = checkHasJobs(clonedForm);

      if (hasJobs) {
        // For step 5, we need to update the specific job in the original form
        updatedForm = cloneDeep(form);

        if (isRiskForm(form) && isRiskForm(clonedForm)) {
          updateRiskFormJob(
            updatedForm as RiskForm,
            clonedForm,
            jobId,
          );
        } else {
          updateTemplateFormJob(
            updatedForm as TemplateForm,
            clonedForm as TemplateForm,
            jobId,
          );
        }
      }
    }

    onClose(true, updatedForm);
  };

  const checkHasJobs = (form: RiskForm | TemplateForm): boolean => {
    return isRiskForm(form)
      ? (form.risk_job?.length || 0) > 0
      : form.template_job.length > 0;
  };

  const updateRiskFormJob = (
    targetForm: RiskForm,
    sourceForm: RiskForm,
    jobId: string | number,
  ) => {
    const jobIndex = parseInt(jobId as string, 10);
    const editedJob = sourceForm.risk_job?.[0];

    if (!isNaN(jobIndex) && targetForm.risk_job && editedJob) {
      targetForm.risk_job[jobIndex] = editedJob;
    }
  };

  const updateTemplateFormJob = (
    targetForm: TemplateForm,
    sourceForm: TemplateForm,
    jobId: string | number,
  ) => {
    const editedJob = sourceForm.template_job?.[0];
    const idx = targetForm.template_job.findIndex(job => job.job_id === jobId);

    if (idx !== -1 && editedJob) {
      targetForm.template_job[idx] = editedJob;
    }
  };

  const StepConfig: Record<number, {component: React.ReactNode}> = {
    1: {
      component: (
        <EditBasicDetailsComp
          clonedForm={clonedForm}
          setClonedForm={setClonedForm}
          type={type}
        />
      ),
    },
    2: {
      component: (
        <RaCategoryStep
          ref={raCategoryRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
          type={type}
        />
      ),
    },
    3: {
      component: (
        <HazardCategoryStep
          ref={hazardCategoryRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
          type={type}
        />
      ),
    },
    4: {
      component: (
        <AtRiskStep
          ref={atRiskRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
        />
      ),
    },
    5: {
      component: (
        <AddJobsStep
          ref={addJobsRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
          jobIndex={jobIndex}
          type={type}
        />
      ),
    },
    6: {
      component: (
        <AddTeamMembersStep
          ref={addTeamMembersRef}
          form={clonedForm as RiskForm}
          setForm={
            setClonedForm as React.Dispatch<React.SetStateAction<RiskForm>>
          }
          onValidate={handleValidation}
          isEdit={true}
        />
      ),
    },
  };

  return (
    <Modal
      show
      onHide={() => onClose(false)}
      size={step === 1 ? 'lg' : 'xl'}
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body className="edit-modal-body">
        {StepConfig[step].component}
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={() => onClose(false)}
        >
          Cancel
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={handleSave}
          disabled={!isFormValid || !hasChanges}
        >
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
