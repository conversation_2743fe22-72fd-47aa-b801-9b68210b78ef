import React, {useState} from 'react';
import {<PERSON>dal, Button, Form} from 'react-bootstrap';
import {toast} from 'react-toastify';
import CustomDatePicker from '../../components/CustomDatePicker';
import {getErrorMessage} from '../../utils/common';

import '../../styles/components/re-assign-approver-modal.scss';

interface SubmitRoutineRAModalProps {
  onConfirm: (params: {
    approveDate: Date;
  }) => Promise<{message?: string} | void>;
  trigger: React.ReactElement;
}

const SubmitRoutineRAModal: React.FC<SubmitRoutineRAModalProps> = ({
  onConfirm,
  trigger,
}) => {
  const [show, setShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [approvalDate, setApprovalDate] = useState<Date | undefined>(undefined);
  const handleSubmit = async (date: Date) => {
    try {
      setIsLoading(true);
      const result = await onConfirm({
        approveDate: date,
      });
      const {message} = result || {};
      if (message) {
        toast.success(message);
      } else {
        toast.success('RA approved successfully.');
      }
    } catch (error) {
      toast.error(
        getErrorMessage(
          error,
          'Failed to approve the RA. Please try again later.',
        ),
      );
    } finally {
      setIsLoading(false);
    }

    handleClose();
  };

  const handleTriggerClick = () => {
    setShow(true);
  };

  const handleClose = () => {
    setShow(false);
    setApprovalDate(undefined);
    setIsLoading(false);
  };

  return (
    <>
      {trigger &&
        React.cloneElement(trigger, {
          onClick: handleTriggerClick,
        })}
      <Modal
        show={show}
        onHide={handleClose}
        size="lg"
        backdrop="static"
        className="reassign-approver-modal"
      >
        <Modal.Header>
          <Modal.Title>Submitting Routine RA</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div
            style={{
              background: '#FDEAEA',
              color: '#D32F2F',
              borderRadius: 4,
              padding: 16,
              marginBottom: 20,
              fontWeight: 500,
              fontSize: 16,
            }}
          >
            <div>
              <strong>
                Do you want to submit this Routine Risk Assessment?
              </strong>
            </div>
            <div style={{fontWeight: 400, fontSize: 14}}>
              Submitting this would mean auto approval and vessel will be
              Notified along with an Automated Email.
            </div>
          </div>
          <Form.Group>
            <CustomDatePicker
              isRequired={true}
              minDate={undefined}
              label="Approval Date"
              value={approvalDate}
              onChange={date => setApprovalDate(date)}
              placeholder="Select Date"
              controlId="approval_date"
              errorMsg=""
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            className="me-2 fs-14"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            className="me-2 fs-14"
            onClick={() => handleSubmit(approvalDate as unknown as Date)}
            disabled={isLoading || !approvalDate}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default SubmitRoutineRAModal;
