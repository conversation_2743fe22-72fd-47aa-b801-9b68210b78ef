import React, {forwardRef, useImperativeHandle} from 'react';
import SelectableCheckboxGrid from '../../components/SelectableCheckboxGrid';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import {useDataStoreContext} from '../../context';

// --- Helper Functions ---

function getCategoryFields(
  form: TemplateForm | RiskForm,
  type: 'template' | 'risk',
) {
  if (type === 'risk') {
    const riskForm = form as RiskForm;
    return {
      category_id: riskForm?.risk_category?.category_id || [],
      is_other: riskForm?.risk_category?.is_other || false,
      value: riskForm?.risk_category?.value || '',
      date_risk_assessment: riskForm?.date_risk_assessment ?? '',
    };
  } else {
    const templateForm = form as TemplateForm;
    return {
      category_id: templateForm?.template_category?.category_id || [],
      is_other: templateForm?.template_category?.is_other || false,
      value: templateForm?.template_category?.value || '',
      date_risk_assessment: '',
    };
  }
}

function setCategoryFields(
  setForm: any,
  type: 'template' | 'risk',
  update:
    | Partial<RiskForm['risk_category']>
    | Partial<TemplateForm['template_category']>,
) {
  if (type === 'risk') {
    setForm((prev: RiskForm) => ({
      ...prev,
      risk_category: {
        ...prev.risk_category,
        ...update,
      },
    }));
  } else {
    setForm((prev: TemplateForm) => ({
      ...prev,
      template_category: {
        ...prev.template_category,
        ...update,
      },
    }));
  }
}

function useCategoryValidation(
  form: TemplateForm | RiskForm,
  type: 'template' | 'risk',
  onValidate?: (valid: boolean) => void,
) {
  const {category_id, is_other, value} = getCategoryFields(form, type);

  const validate = React.useCallback(() => {
    let valid = category_id.length > 0 || is_other;
    if (is_other) {
      valid = !!value.trim();
    }
    if (onValidate) onValidate(valid);
    return valid;
  }, [category_id, is_other, value, onValidate]);

  React.useEffect(() => {
    validate();
    // eslint-disable-next-line
  }, [category_id, is_other, value]);

  return validate;
}

// --- Main Component ---

export const RaCategoryStep = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      isEdit = false,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      isEdit?: boolean;
      type?: 'template' | 'risk';
    },
    ref,
  ) => {
    const {
      dataStore: {riskCategoryList},
    } = useDataStoreContext();

    const categoryFields = getCategoryFields(form, type);
    const validate = useCategoryValidation(form, type, onValidate);

    useImperativeHandle(ref, () => ({
      validate,
    }));

    const handleOthersChange = (flag: boolean, value: string) => {
      setCategoryFields(setForm, type, {
        is_other: flag,
        value: flag ? value : '',
      });
    };

    const handleCheckedChange = (ids: number[]) => {
      setCategoryFields(setForm, type, {
        category_id: ids,
      });
    };

    return (
      <SelectableCheckboxGrid
        title={form?.task_requiring_ra || ''}
        subtitle="Select all the R.A. Category"
        searchPlaceholder="Search RA Category"
        options={riskCategoryList}
        initialChecked={categoryFields.category_id}
        isOthersSelected={categoryFields.is_other}
        othersText={categoryFields.value}
        onOthersChange={handleOthersChange}
        onChange={handleCheckedChange}
        hasOthers={true}
        isEdit={isEdit}
        dateOfRiskAssessment={categoryFields.date_risk_assessment}
        feildType="category"
        type={type}
        hasTemplateId={!!(form as RiskForm)?.template_id}
        isLeve1RA={(form as RiskForm)?.ra_level === 4}
      />
    );
  },
);

RaCategoryStep.displayName = 'RaCategoryStep';
