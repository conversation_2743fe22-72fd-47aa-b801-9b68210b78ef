import React, {useEffect, useRef, useState} from 'react';
import * as _ from 'lodash';
import {toast} from 'react-toastify';
import {IDataStoreContext, useDataStoreContext} from '../../context';
import GenericStepper, {StepConfig} from '../../components/GenericStepper';
import {BasicDetails} from './BasicDetails';
import {AddJobsStep} from './AddJobsStep';
import {AtRiskStep} from './AtRiskStep';
import {HazardCategoryStep} from './HazardCategoryStep';
import {RaCategoryStep} from './RaCategoryStep';
import {RiskRatingStep} from './RiskRatingStep';
import {
  createNewRA,
  createNewTemplate,
  getApprovalsRequiredList,
  getCrewList,
  getHazardsList,
  getMainRiskParameterType,
  getOfficesList,
  getRiskById,
  getRiskCategoryList,
  getRiskParameterType,
  getTaskReliabilityAssessList,
  getTemplateById,
  getVesselsList,
  updateSavedRA,
  updateSavedTemplate,
} from '../../services/services';
import {useLocation, useNavigate} from 'react-router-dom';
import {TemplateForm} from '../../types/template';
import {RAItemFull, RiskForm} from '../../types/risk';
import PreviewFormDetails from './PreviewFormDetails';
import {ConfirmPublishDetailsModal} from '../../components/ConfirmPublishDetailsModal';
import {TemplateFormStatus} from '../../enums';
import {
  calculateRiskRating,
  createFormFromData,
  createRiskFormFromData,
  formParameterHandler,
  transformTemplateToRisk,
} from '../../utils/helper';
import {AddTeamMembersStep} from './AddTeamMembersStep';
import Loader from '../../components/Loader';
import {ExitCreateRiskPageModal} from '../../components/ExitCreateRiskPageModal';
import {getRiskRatingValue} from '../../utils/common';

// --- Helper Functions ---
type TypeOption = 'template' | 'risk';
export function getTypeAndIds(pathname: string) {
  let type: TypeOption = 'template';
  let templateFormID: string | null = null;
  let defaultTemplateId: string | null = null;

  // Only match /risk-assessment/templates/<numeric_id>
  const templateWithIdRegex = /^\/risk-assessment\/templates\/(\d+)$/;
  const riskWithIdRegex = /^\/risk-assessment\/risks\/(\d+)$/;
  const matchCreateRAUsingTemplate =
    /^\/risk-assessment\/templates\/(\d+)\/risks\/create$/.exec(pathname);

  if (matchCreateRAUsingTemplate) {
    defaultTemplateId = matchCreateRAUsingTemplate[1];
    type = 'risk';
  } else if (templateWithIdRegex.test(pathname)) {
    const match = templateWithIdRegex.exec(pathname);
    templateFormID = match ? match[1] : null;
  } else if (/^\/risk-assessment\/risks\/create$/.test(pathname)) {
    type = 'risk';
  } else if (riskWithIdRegex.test(pathname)) {
    const match = riskWithIdRegex.exec(pathname);
    templateFormID = match ? match[1] : null;
    type = 'risk';
  }
  return {type, templateFormID, defaultTemplateId};
}

function useInitialForm(type: TypeOption) {
  return useState<TemplateForm | RiskForm>(
    type === 'risk' ? createRiskFormFromData() : createFormFromData(),
  );
}

function useRefs() {
  return {
    basicDetailsRef: useRef<any>(null),
    addTeamMembersRef: useRef<any>(null),
    raCategoryRef: useRef<any>(null),
    hazardCategoryRef: useRef<any>(null),
    atRiskRef: useRef<any>(null),
    addJobs: useRef<any>(null),
  };
}

function getSteps({
  type,
  refs,
  form,
  setForm,
  setStepValid,
}: {
  type: TypeOption;
  refs: any;
  form: TemplateForm | RiskForm;
  setForm: any;
  setStepValid: (valid: boolean) => void;
}): StepConfig[] {
  const baseSteps: StepConfig[] = [
    {
      label: type === 'risk' ? 'Confirm Basic Details' : 'Basic Details',
      component: (
        <BasicDetails
          ref={refs.basicDetailsRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label: type === 'risk' ? 'Confirm RA Category' : 'Identify RA Category',
      component: (
        <RaCategoryStep
          ref={refs.raCategoryRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label:
        type === 'risk'
          ? 'Confirm Hazard Category'
          : 'Identify Hazard Category',
      component: (
        <HazardCategoryStep
          ref={refs.hazardCategoryRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label: 'Who & What is At Risk',
      component: (
        <AtRiskStep
          ref={refs.atRiskRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
        />
      ),
    },
    {
      label:
        type === 'risk' ? 'Confirm Associated Jobs' : 'Add Associated Jobs',
      component: (
        <AddJobsStep
          form={form}
          setForm={setForm}
          ref={refs.addJobs}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label: 'Overall Risk Rating',
      component: (
        <RiskRatingStep
          ref={refs.atRiskRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
        />
      ),
    },
  ];

  if (type === 'risk') {
    return [
      baseSteps[0],
      {
        label: 'Add Team Members',
        component: (
          <AddTeamMembersStep
            ref={refs.addTeamMembersRef}
            form={form as RiskForm}
            setForm={setForm as React.Dispatch<React.SetStateAction<RiskForm>>}
            onValidate={setStepValid}
          />
        ),
      },
      ...baseSteps.slice(1),
    ];
  }
  return baseSteps;
}

type FetchAndSetTemplateDataOptions = {
  templateFormID: string | null;
  type: TypeOption;
  setForm: any;
  setLoading: any;
  setLoadStep: any;
  setOpenPreview: any;
  defaultTemplateId: string | null;
  steps: StepConfig[];
  validateStep: (step: number) => boolean;
  setDataStore: React.Dispatch<
    React.SetStateAction<IDataStoreContext['dataStore']>
  >;
  canExportPDF: boolean;
  setIsApprovedId: React.Dispatch<React.SetStateAction<string>>;
};
async function fetchAndSetTemplateData({
  templateFormID,
  type,
  setForm,
  setLoading,
  setLoadStep,
  setOpenPreview,
  defaultTemplateId,
  steps,
  validateStep,
  setDataStore,
  canExportPDF = false,
  setIsApprovedId,
}: FetchAndSetTemplateDataOptions) {
  if (!templateFormID || !type) return;
  setLoading(true);
  try {
    const response =
      type === 'risk'
        ? await getRiskById(templateFormID)
        : await getTemplateById(templateFormID);
    const data = response.result;

    const formData =
      type === 'risk' ? createRiskFormFromData(data) : createFormFromData(data);

    if (type === 'risk') {
      await handleRiskTypeData({
        data,
        formData: formData as RiskForm,
        templateFormID,
        canExportPDF,
        setIsApprovedId,
        setDataStore,
      });
    }

    setForm(formData);

    if (defaultTemplateId) {
      setLoadStep(1);
      validateStep(1);
    } else if (
      (data as RAItemFull)?.draft_step &&
      Number((data as RAItemFull)?.draft_step) < steps.length + 1
    ) {
      setLoadStep((data as RAItemFull).draft_step ?? 1);
      validateStep((data as RAItemFull).draft_step ?? 1);
    } else setOpenPreview(true);
  } catch (err) {
    console.error('Error fetching draft', err);
  } finally {
    setLoading(false);
  }
}

async function handleRiskTypeData({
  data,
  formData,
  templateFormID,
  canExportPDF,
  setIsApprovedId,
  setDataStore,
}: {
  data: any;
  formData: RiskForm;
  templateFormID: string;
  canExportPDF: boolean;
  setIsApprovedId: React.Dispatch<React.SetStateAction<string>>;
  setDataStore: React.Dispatch<
    React.SetStateAction<IDataStoreContext['dataStore']>
  >;
}) {
  if (data?.status === 3 && canExportPDF) {
    setIsApprovedId(templateFormID);
  }
  const vesselId = formData.vessel_id;
  if (vesselId) {
    const resp = await getCrewList(vesselId);
    setDataStore(prev => ({
      ...prev,
      crewMembersListForRisk: resp,
    }));
  }
}

export function getPrimaryBtnTitle(
  currentStep: number,
  lastStep: number,
  type: TypeOption,
) {
  if (currentStep === lastStep) {
    return type === 'risk' ? 'Preview' : 'Preview Template';
  }
  return 'Next';
}

// --- Main Component ---
export const StepperPage = () => {
  const {pathname} = useLocation();
  const {type, templateFormID, defaultTemplateId} = getTypeAndIds(pathname);
  const {
    setDataStore,
    roleConfig: {riskAssessment: {canExportPDF} = {canExportPDF: false}} = {},
  } = useDataStoreContext();
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const navigate = useNavigate();
  const [openPreview, setOpenPreview] = useState(false);
  const [stepValid, setStepValid] = useState(false);
  const [loadStep, setLoadStep] = useState(1);
  const [showConfirmPublishDetailsModal, setShowConfirmPublishDetailsModal] =
    useState(false);
  const [keywords, setKeywords] = useState<string[]>([]);
  const [form, setForm] = useInitialForm(type);
  const refs = useRefs();
  const [showExitRiskPageModal, setShowExitRiskPageModal] = useState(false);
  const [isApprovedId, setIsApprovedId] = useState('');
  console.log('isApprovedId', isApprovedId);

  useEffect(() => {
    if (defaultTemplateId?.length) {
      setLoading(true);
      getTemplateById(defaultTemplateId)
        .then(defualtTemplate => {
          if (defualtTemplate.result) {
            const data = transformTemplateToRisk(defualtTemplate.result);
            setForm(data);
          }
        })
        .catch(err => {
          console.error('Error fetching default template:', err);
        })
        .finally(() => setLoading(false));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, defaultTemplateId]);
  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        setLoading(true);
        const results = await Promise.allSettled([
          getRiskCategoryList(),
          getHazardsList(),
          getRiskParameterType(),
          getTaskReliabilityAssessList(),
          getMainRiskParameterType(),
          getMainRiskParameterType(true),
          getVesselsList(),
          getOfficesList(),
          getApprovalsRequiredList(1),
          getApprovalsRequiredList(2),
        ]);

        // Check if any promises were rejected
        const rejectedResults = results.filter(
          result => result.status === 'rejected',
        );
        if (rejectedResults.length > 0) {
          // Log the first rejection reason for debugging
          const firstRejection = rejectedResults[0] as PromiseRejectedResult;
          throw new Error(
            firstRejection.reason?.message || 'Failed to load data',
          );
        }

        const [
          categorListData,
          hazardsListData,
          riskParameterData,
          taskRelAssessData,
          mainRiskParameterData,
          mainRiskParameterDataForRiskRating,
          vesselResponse,
          officeResponse,
          approvalListOffice,
          approvalListVessel,
        ] = results.map(result =>
          result.status === 'fulfilled' ? result.value : [],
        );

        const groupedRiskParameterData = _.chain(riskParameterData)
          .groupBy(item => item?.parameter_type?.id)
          .map(items => ({
            id: items[0]?.parameter_type?.id,
            name: items[0]?.parameter_type?.name,
            parameters: items?.map(i => ({
              id: i?.id,
              name: i?.name,
            })),
          }))
          .value();

        setDataStore((prev: any) => ({
          ...prev,
          riskCategoryList: categorListData,
          hazardsList: hazardsListData,
          riskParameterType: groupedRiskParameterData,
          taskReliabilityAssessList: taskRelAssessData,
          riskParameterList: mainRiskParameterData,
          riskParameterListForRiskRaiting: mainRiskParameterDataForRiskRating,
          vesselListForRisk: vesselResponse,
          officeListForRisk: officeResponse,
          approversReqListForRiskOffice: approvalListOffice,
          approversReqListForRiskVessel: approvalListVessel,
        }));
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    loadBasicDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const steps = getSteps({
    type,
    refs,
    form,
    setForm,
    setStepValid,
  });

  const validateStep = (step: number): boolean => {
    const stepRefs =
      type === 'risk'
        ? [
            refs.basicDetailsRef,
            refs.addTeamMembersRef,
            refs.raCategoryRef,
            refs.hazardCategoryRef,
            refs.atRiskRef,
            refs.addJobs,
          ]
        : [
            refs.basicDetailsRef,
            refs.raCategoryRef,
            refs.hazardCategoryRef,
            refs.atRiskRef,
            refs.addJobs,
          ];

    if (step >= 1 && step <= stepRefs.length) {
      const ref = stepRefs[step - 1];
      if (ref.current && !ref.current.validate()) {
        setStepValid(false);
        return false;
      }
    }
    setStepValid(true);
    return true;
  };

  useEffect(() => {
    fetchAndSetTemplateData({
      templateFormID,
      type,
      setForm,
      setLoading,
      setLoadStep,
      setOpenPreview,
      defaultTemplateId,
      steps,
      validateStep,
      setDataStore,
      canExportPDF,
      setIsApprovedId,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templateFormID, type]);

  const handlePreview = () => setOpenPreview(true);

  const handleNext = async (currentStep: number): Promise<void> => {
    if (currentStep === 1 && type === 'risk') {
      const vesselId = (form as RiskForm).vessel_id;
      if (vesselId) {
        setLoader(true);
        const resp = await getCrewList(vesselId);
        setDataStore(prev => ({
          ...prev,
          crewMembersListForRisk: resp,
        }));
        setLoader(false);
      }
    }
    window.scrollTo({top: 0, behavior: 'smooth'});
    validateStep(currentStep);
  };

  const buildDraftPayload = (
    form: TemplateForm | RiskForm,
    currentStep: number,
    defaultTemplateId: string | null,
    type: TypeOption,
  ) => {
    const payload = {
      ...form,
      draft_step: currentStep,
    };
    if ('vessel_id' in payload && !payload?.vessel_id) {
      delete (payload as any).vessel_id;
    }
    if (defaultTemplateId && type === 'risk') {
      (payload as any).template_id = defaultTemplateId;
    }
    return payload;
  };

  const updateDraft = async (
    templateFormID: string,
    type: TypeOption,
    payload: any,
  ) => {
    if (type === 'risk') {
      await updateSavedRA(templateFormID, payload);
    } else {
      await updateSavedTemplate(templateFormID, payload);
    }
  };

  const createDraft = async (type: TypeOption, payload: any) => {
    if (type === 'risk') {
      return await createNewRA(payload);
    }
    return await createNewTemplate(payload);
  };

  const updateUrlAndForm = (type: TypeOption, result: any, setForm: any) => {
    if (!result?.id) return;
    const url =
      type === 'template'
        ? `/risk-assessment/templates/${result.id}${window.location.search}`
        : `/risk-assessment/risks/${result.id}${window.location.search}`;
    window.history.replaceState({}, '', url);
    setForm((formData: any) => ({
      ...formData,
      id: result.id,
    }));
  };

  const handleSaveToDraft = async (currentStep: number): Promise<void> => {
    validateStep(currentStep);
    const payload = buildDraftPayload(
      form,
      currentStep,
      defaultTemplateId,
      type,
    );
    await formParameterHandler(payload);

    const alertMessage = type === 'risk' ? 'RA' : 'Template';
    setLoader(true);

    try {
      if (templateFormID) {
        await updateDraft(templateFormID, type, payload);
        toast.success(`${alertMessage} draft updated successfully`);
      } else {
        const result = await createDraft(type, payload);
        toast.success(`${alertMessage} draft saved successfully`);
        updateUrlAndForm(type, result, setForm);
      }

      if (currentStep > steps.length) {
        navigate(
          `/risk-assessment${type === 'risk' ? '' : '/template-listing'}`,
        );
      }
    } catch (err) {
      toast.error('Error saving draft. Changes were not saved');
      console.error('Error saving draft:', err);
    } finally {
      setLoader(false);
    }
  };

  const handelFormPublish = async (updatedData: string[]) => {
    let riskRating = '';
    if (type === 'risk') riskRating = calculateRiskRating(form);
    setKeywords(updatedData);
    const payload = {
      ...form,
      status: TemplateFormStatus.PUBLISHED,
      risk_rating: riskRating ? getRiskRatingValue(riskRating) : undefined,
    };
    await formParameterHandler(payload);
    if (type !== 'risk') (payload as any).template_keyword = updatedData;
    if (defaultTemplateId && type === 'risk')
      (payload as any).template_id = defaultTemplateId;
    const alertMessage = type === 'risk' ? 'RA' : 'Template';
    try {
      setLoader(true);
      if (templateFormID) {
        type === 'risk'
          ? await updateSavedRA(templateFormID, payload)
          : await updateSavedTemplate(templateFormID, payload);
      } else {
        type === 'risk'
          ? await createNewRA(payload)
          : await createNewTemplate(payload);
      }
      setLoader(false);
      toast.success(`${alertMessage} Published Successfully`);
      setShowConfirmPublishDetailsModal(false);
      navigate(`/risk-assessment${type === 'risk' ? '' : '/template-listing'}`);
    } catch (err) {
      setLoader(false);
      toast.error(`Error while publishing the ${alertMessage}`);
      console.error('Error publishing template:', err);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <div
          className="spinner-border text-primary"
          data-testid="loading-spinner"
          aria-label="Loading"
        />
      </div>
    );
  }
  const handleClose = () => {
    let pathSegment: string;
    if (templateFormID) {
      pathSegment = '/drafts';
      navigate(`/risk-assessment${pathSegment}`);
    } else if (type === 'risk') {
      navigate(`/risk-assessment`);
    } else {
      pathSegment = '/template-listing';
      navigate(`/risk-assessment${pathSegment}`);
    }
  };

  return (
    <>
      {loader && <Loader isOverlayLoader />}
      {!openPreview && (
        <GenericStepper
          breadCrumbTitle={
            type === 'risk'
              ? 'Creating Risk Assessment'
              : 'Creating Risk Assessment Template'
          }
          steps={steps}
          onNext={handleNext}
          onClose={() =>
            !templateFormID ? setShowExitRiskPageModal(true) : handleClose()
          }
          primaryBtnOnClick={handlePreview}
          secondaryBtnOnClick={handleSaveToDraft}
          primaryBtnTitle={(currentStep: number, lastStep: number) =>
            getPrimaryBtnTitle(currentStep, lastStep, type)
          }
          secondaryBtnTitle="Save to Draft"
          primaryBtnDisabled={!stepValid}
          secondaryBtnDisabled={
            !(
              form?.task_requiring_ra &&
              form.task_requiring_ra.trim().length > 0
            )
          }
          onStepChange={handleNext}
          defaultLoadStep={loadStep}
        />
      )}
      {openPreview && (
        <PreviewFormDetails
          form={form}
          setForm={setForm as any}
          atRiskRef={refs?.atRiskRef}
          handlePreviewPublush={() => {
            type === 'risk'
              ? handelFormPublish([])
              : setShowConfirmPublishDetailsModal(true);
          }}
          handleSaveToDraft={handleSaveToDraft}
          type={type}
        />
      )}
      {showConfirmPublishDetailsModal && (
        <ConfirmPublishDetailsModal
          onClose={() => {
            setShowConfirmPublishDetailsModal(false);
          }}
          keywords={keywords}
          onSave={handelFormPublish}
        />
      )}
      {showExitRiskPageModal && (
        <ExitCreateRiskPageModal
          onClose={() => setShowExitRiskPageModal(false)}
          handleSubmit={handleClose}
        />
      )}
    </>
  );
};
