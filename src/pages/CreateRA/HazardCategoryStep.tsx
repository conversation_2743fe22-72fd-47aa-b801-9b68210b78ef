import React, {forwardRef, useImperativeHandle} from 'react';
import SelectableCheckboxGrid from '../../components/SelectableCheckboxGrid';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import {useDataStoreContext} from '../../context';

// --- Helper Functions ---

function getHazardFields(
  form: TemplateForm | RiskForm,
  type: 'template' | 'risk',
) {
  if (type === 'risk') {
    const riskForm = form as RiskForm;
    return {
      hazard_id: riskForm?.risk_hazard?.hazard_id || [],
      is_other: riskForm?.risk_hazard?.is_other ?? false,
      value: riskForm?.risk_hazard?.value || '',
      date_risk_assessment: riskForm?.date_risk_assessment ?? '',
    };
  } else {
    const templateForm = form as TemplateForm;
    return {
      hazard_id: templateForm?.template_hazard?.hazard_id || [],
      is_other: templateForm?.template_hazard?.is_other ?? false,
      value: templateForm?.template_hazard?.value || '',
      date_risk_assessment: '',
    };
  }
}

function setHazardFields(
  setForm: any,
  type: 'template' | 'risk',
  update:
    | Partial<RiskForm['risk_hazard']>
    | Partial<TemplateForm['template_hazard']>,
) {
  if (type === 'risk') {
    setForm((prev: RiskForm) => ({
      ...prev,
      risk_hazard: {
        ...prev.risk_hazard,
        ...update,
      },
    }));
  } else {
    setForm((prev: TemplateForm) => ({
      ...prev,
      template_hazard: {
        ...prev.template_hazard,
        ...update,
      },
    }));
  }
}

function useHazardValidation(
  form: TemplateForm | RiskForm,
  type: 'template' | 'risk',
  onValidate?: (valid: boolean) => void,
) {
  const {hazard_id, is_other, value} = getHazardFields(form, type);

  const validate = React.useCallback(() => {
    let valid = hazard_id.length > 0 || is_other;
    if (is_other) {
      valid = !!value.trim();
    }
    if (onValidate) onValidate(valid);
    return valid;
  }, [hazard_id, is_other, value, onValidate]);

  React.useEffect(() => {
    validate();
    // eslint-disable-next-line
  }, [hazard_id, is_other, value]);

  return validate;
}

// --- Main Component ---

export const HazardCategoryStep = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      isEdit = false,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      isEdit?: boolean;
      type?: 'template' | 'risk';
    },
    ref,
  ) => {
    const {
      dataStore: {hazardsList},
    } = useDataStoreContext();

    const hazardCategories = hazardsList || [];
    const hazardFields = getHazardFields(form, type);

    const validate = useHazardValidation(form, type, onValidate);

    useImperativeHandle(ref, () => ({
      validate,
    }));

    const handleOthersChange = (flag: boolean, value: string) => {
      setHazardFields(setForm, type, {
        is_other: flag,
        value: flag ? value : '',
      });
    };

    const handleCheckedChange = (ids: number[]) => {
      setHazardFields(setForm, type, {
        hazard_id: ids,
      });
    };

    return (
      <SelectableCheckboxGrid
        title={form?.task_requiring_ra || ''}
        subtitle="Select all the possible Hazard Category"
        searchPlaceholder="Search Hazard Category"
        options={hazardCategories}
        initialChecked={hazardFields.hazard_id}
        isOthersSelected={hazardFields.is_other}
        othersText={hazardFields.value}
        onOthersChange={handleOthersChange}
        onChange={handleCheckedChange}
        hasOthers={true}
        isEdit={isEdit}
        dateOfRiskAssessment={hazardFields.date_risk_assessment}
        type={type}
        feildType="hazard"
        hasTemplateId={!!(form as RiskForm)?.template_id}
        isLeve1RA={(form as RiskForm)?.ra_level === 4}
      />
    );
  },
);

HazardCategoryStep.displayName = 'HazardCategoryStep';
