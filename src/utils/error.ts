import {AxiosError} from 'axios';

/**
 * Extracts a user-friendly error message from various error types.
 * Prioritizes server-provided messages over generic error messages.
 *
 * @param {any} error - The error object to extract message from
 * @returns {string} A user-friendly error message
 *
 * @example
 * try {
 *   await apiCall();
 * } catch (error) {
 *   const message = extractErrorMessage(error);
 *   showNotification(message);
 * }
 */
export const extractErrorMessage = (error: any) => {
  let message = 'Something went wrong. Please try again';

  if (error?.message) message = error.message;

  if (error?.isAxiosError) {
    const axiosError: AxiosError<any> = error;

    if (axiosError.message) message = axiosError.message;

    if (axiosError.response?.data?.message)
      message = axiosError.response.data.message;
  }

  return message;
};
