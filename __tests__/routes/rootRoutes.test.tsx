import React from 'react';
import {render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import AppRoutes from '../../src/routes/rootRoutes';
import {useDataStoreContext} from '../../src/context';

// Mock the context
jest.mock('../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

// Mock route.config
jest.mock('../../src/routes/route.config', () => {
  return {
    __esModule: true,
    default: jest.fn(),
  };
});

const mockUseDataStoreContext = useDataStoreContext as jest.MockedFunction<typeof useDataStoreContext>;
const mockRoutesConfig = require('../../src/routes/route.config').default as jest.MockedFunction<any>;

// Sample components for testing
const MockComponent = () => <div>Mock Component</div>;
const MockChildComponent = () => <div>Mock Child Component</div>;
const MockParentComponent = () => <div>Mock Parent Component</div>;

describe('AppRoutes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDataStoreContext.mockReturnValue({
      roleConfig: {
        riskAssessment: { hasPermision: true }
      }
    } as any);
  });

  describe('Basic Functionality', () => {
    it('renders without crashing', () => {
      mockRoutesConfig.mockReturnValue([]);

      expect(() => {
        render(
          <MemoryRouter initialEntries={['/']}>
            <AppRoutes />
          </MemoryRouter>
        );
      }).not.toThrow();
    });

    it('renders for unknown routes', () => {
      mockRoutesConfig.mockReturnValue([]);

      expect(() => {
        render(
          <MemoryRouter initialEntries={['/unknown']}>
            <AppRoutes />
          </MemoryRouter>
        );
      }).not.toThrow();
    });

    it('passes roleConfig to routesConfig function', () => {
      const mockRoleConfig = {
        riskAssessment: { hasPermision: true }
      };

      mockUseDataStoreContext.mockReturnValue({
        roleConfig: mockRoleConfig
      } as any);

      mockRoutesConfig.mockReturnValue([]);

      render(
        <MemoryRouter initialEntries={['/test']}>
          <AppRoutes />
        </MemoryRouter>
      );

      expect(mockRoutesConfig).toHaveBeenCalledWith(mockRoleConfig);
    });
  });

  describe('RouteGenerator Coverage', () => {
    it('covers isPermission false branch', () => {
      mockRoutesConfig.mockReturnValue([
        {
          path: '/no-permission',
          component: MockComponent,
          isPermission: false,
        },
      ]);

      expect(() => {
        render(
          <MemoryRouter initialEntries={['/no-permission']}>
            <AppRoutes />
          </MemoryRouter>
        );
      }).not.toThrow();
    });

    it('covers redirect branch', () => {
      mockRoutesConfig.mockReturnValue([
        {
          path: '/redirect',
          redirect: '/target',
        },
      ]);

      expect(() => {
        render(
          <MemoryRouter initialEntries={['/redirect']}>
            <AppRoutes />
          </MemoryRouter>
        );
      }).not.toThrow();
    });

    it('covers component with childRoutes branch', () => {
      mockRoutesConfig.mockReturnValue([
        {
          path: '/parent',
          component: MockParentComponent,
          childRoutes: [
            {
              path: 'child',
              component: MockChildComponent,
            },
          ],
        },
      ]);

      expect(() => {
        render(
          <MemoryRouter initialEntries={['/parent']}>
            <AppRoutes />
          </MemoryRouter>
        );
      }).not.toThrow();
    });

    it('covers component without childRoutes branch', () => {
      mockRoutesConfig.mockReturnValue([
        {
          path: '/simple',
          component: MockComponent,
        },
      ]);

      expect(() => {
        render(
          <MemoryRouter initialEntries={['/simple']}>
            <AppRoutes />
          </MemoryRouter>
        );
      }).not.toThrow();
    });

    it('covers route without component or redirect', () => {
      mockRoutesConfig.mockReturnValue([
        {
          path: '/empty',
          // No component or redirect - this covers the undefined return
        },
      ]);

      expect(() => {
        render(
          <MemoryRouter initialEntries={['/empty']}>
            <AppRoutes />
          </MemoryRouter>
        );
      }).not.toThrow();
    });
  });

});
