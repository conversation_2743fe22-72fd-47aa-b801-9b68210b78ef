import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ArchiveTemplateModal from '../../../../src/pages/RATemplateListing/components/ArchiveTemplateModal';
import {markTemplateAsArchived} from '../../../../src/services/services';

// Mock services
jest.mock('../../../../src/services/services', () => ({
  markTemplateAsArchived: jest.fn(),
}));

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock SingleBadgePopover component
jest.mock('../../../../src/components/SingleBadgePopover', () => {
  return function MockSingleBadgePopover({
    items,
    label,
  }: {
    items: string[];
    label: string;
  }) {
    return (
      <div data-testid="single-badge-popover">
        <span data-testid="popover-label">{label}</span>
        <div data-testid="popover-items">{items.join(', ')}</div>
      </div>
    );
  };
});

// Mock TruncateBasicText component
jest.mock('../../../../src/components/TruncateBasicText', () => {
  return function MockTruncateText({text}: {text: string}) {
    return <span data-testid="truncate-text">{text}</span>;
  };
});

// Mock raLevelLabel to prevent undefined error in RAFilters
jest.mock('../../../../src/utils/common', () => ({
  ...jest.requireActual('../../../../src/utils/common'),
  parseDate: jest.fn((date: string) => {
    // Simple date formatting for tests
    const d = new Date(date);
    return d.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  }),
  raLevelLabel: {
    1: 'Routine',
    2: 'Special',
    3: 'Critical',
    4: 'Level 1 RA',
  },
}));

// Mock getInitials utility
jest.mock('../../../../src/utils/user', () => ({
  getInitials: jest.fn((name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  }),
}));

describe('ArchiveTemplateModal', () => {
  const mockMarkTemplateAsArchived =
    markTemplateAsArchived as jest.MockedFunction<
      typeof markTemplateAsArchived
    >;

  const defaultProps = {
    trigger: <button>Archive Template</button>,
    templateId: 1,
    templateName: 'Test Template',
    riskCategories: [
      {
        id: 1,
        template_id: 1,
        category_id: 1,
        category_is_other: false,
        status: 1,
        category: {id: 1, name: 'Risk 1', type: 1},
        value: null,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15',
        updatedAt: '2023-01-15',
      },
      {
        id: 2,
        template_id: 1,
        category_id: 2,
        category_is_other: false,
        status: 1,
        category: {id: 2, name: 'Risk 2', type: 1},
        value: null,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15',
        updatedAt: '2023-01-15',
      },
    ],
    hazardCategories: [
      {
        id: 1,
        template_id: 1,
        hazard_id: 1,
        hazard_category_is_other: false,
        status: 1,
        value: null,
        hazard_detail: {id: 1, name: 'Hazard 1', type: 1},
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15',
        updatedAt: '2023-01-15',
      },
      {
        id: 2,
        template_id: 1,
        hazard_id: 2,
        hazard_category_is_other: false,
        status: 1,
        value: null,
        hazard_detail: {id: 2, name: 'Hazard 2', type: 1},
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15',
        updatedAt: '2023-01-15',
      },
    ],
    keywords: [
      {
        id: 1,
        template_id: 1,
        name: 'keyword1',
        status: 1,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15',
        updatedAt: '2023-01-15',
      },
      {
        id: 2,
        template_id: 1,
        name: 'keyword2',
        status: 1,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15',
        updatedAt: '2023-01-15',
      },
      {
        id: 3,
        template_id: 1,
        name: 'keyword3',
        status: 1,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-15',
        updatedAt: '2023-01-15',
      },
    ],
    createdOn: '2023-01-15',
    userName: 'John Doe',
  };

  const renderComponent = (props = {}) => {
    return render(<ArchiveTemplateModal {...defaultProps} {...props} />);
  };

  beforeEach(() => {
    // Mock console.log to avoid cluttering test output
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});

    // Reset mocks
    mockMarkTemplateAsArchived.mockClear();
    mockMarkTemplateAsArchived.mockResolvedValue({success: true});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the trigger element', () => {
      renderComponent();
      expect(screen.getByText('Archive Template')).toBeInTheDocument();
    });

    it('does not show modal initially', () => {
      renderComponent();
      expect(screen.queryByText('Archive Template')).toBeInTheDocument();
      expect(
        screen.queryByText('Do you really want to Archive this template?'),
      ).not.toBeInTheDocument();
    });

    it('renders with custom trigger element', () => {
      const customTrigger = <span>Custom Trigger</span>;
      renderComponent({trigger: customTrigger});
      expect(screen.getByText('Custom Trigger')).toBeInTheDocument();
    });
  });

  describe('Modal Functionality', () => {
    it('opens modal when trigger is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(
        screen.getByText(
          'Do you really want to Archive this template? This action is not revertible.',
        ),
      ).toBeInTheDocument();
    });

    it('closes modal when Cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      // Close modal
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      await waitFor(() => {
        expect(
          screen.queryByText('Do you really want to Archive this template?'),
        ).not.toBeInTheDocument();
      });
    });

    it('closes modal when clicking outside (onHide)', async () => {
      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      fireEvent.click(trigger);

      // Find modal backdrop and click it
      const modal = document.querySelector('.modal');
      if (modal) {
        fireEvent.click(modal);
      }

      await waitFor(() => {
        expect(
          screen.queryByText('Do you really want to Archive this template?'),
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('Template Details Display', () => {
    it('displays template name correctly', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    it('displays risk categories count', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Risk Categories: 2')).toBeInTheDocument();
    });

    it('displays hazard categories count', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Hazard Categories: 2')).toBeInTheDocument();
    });

    it('displays created on date', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Created on: 15 Jan 2023')).toBeInTheDocument();
    });

    it('displays keywords through SingleBadgePopover', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Keywords: 3')).toBeInTheDocument();
      expect(screen.getByTestId('popover-items')).toHaveTextContent(
        'keyword1, keyword2, keyword3',
      );
    });
  });

  describe('Archive Functionality', () => {
    it('calls markTemplateAsArchived service when Move to Archive button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      // Click archive button
      const archiveButton = screen.getByText('Move to Archive');
      await user.click(archiveButton);

      // Verify service was called with correct template ID
      await waitFor(() => {
        expect(mockMarkTemplateAsArchived).toHaveBeenCalledWith(1);
      });
    });

    it('closes modal after successful archiving', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      // Click archive button
      const archiveButton = screen.getByText('Move to Archive');
      await user.click(archiveButton);

      // Modal should close
      await waitFor(() => {
        expect(
          screen.queryByText(
            'Do you really want to Archive this template? This action is not revertible.',
          ),
        ).not.toBeInTheDocument();
      });
    });

    it('calls onSuccess callback after successful archiving', async () => {
      const onSuccessMock = jest.fn();
      const user = userEvent.setup();
      renderComponent({onSuccess: onSuccessMock});

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      // Click archive button
      const archiveButton = screen.getByText('Move to Archive');
      await user.click(archiveButton);

      // Verify onSuccess was called
      await waitFor(() => {
        expect(onSuccessMock).toHaveBeenCalled();
      });
    });

    it('handles archiving error gracefully', async () => {
      const user = userEvent.setup();
      mockMarkTemplateAsArchived.mockRejectedValue(new Error('Network error'));

      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      // Click archive button
      const archiveButton = screen.getByText('Move to Archive');
      await user.click(archiveButton);

      // Verify error was logged
      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith(
          'Error archiving template:',
          expect.any(Error),
        );
      });

      // Modal should still close
      await waitFor(() => {
        expect(
          screen.queryByText(
            'Do you really want to Archive this template? This action is not revertible.',
          ),
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('Props Variations', () => {
    it('handles empty keywords array', async () => {
      const user = userEvent.setup();
      renderComponent({keywords: []});

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Keywords: 0')).toBeInTheDocument();
      expect(screen.getByTestId('popover-items')).toHaveTextContent('');
    });

    it('handles single keyword', async () => {
      const user = userEvent.setup();
      renderComponent({
        keywords: [
          {
            id: 1,
            template_id: 1,
            name: 'single-keyword',
            status: 1,
            created_by: 'user1',
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
            createdAt: '2023-01-15',
            updatedAt: '2023-01-15',
          },
        ],
      });

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Keywords: 1')).toBeInTheDocument();
      expect(screen.getByTestId('popover-items')).toHaveTextContent(
        'single-keyword',
      );
    });

    it('handles zero risk and hazard categories', async () => {
      const user = userEvent.setup();
      renderComponent({riskCategories: [], hazardCategories: []});

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Risk Categories: 0')).toBeInTheDocument();
      expect(screen.getByText('Hazard Categories: 0')).toBeInTheDocument();
    });

    it('handles long template name', async () => {
      const user = userEvent.setup();
      const longName =
        'This is a very long template name that might cause display issues';
      renderComponent({templateName: longName});

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText(longName)).toBeInTheDocument();
    });

    it('handles special characters in template name', async () => {
      const user = userEvent.setup();
      const specialName = 'Template & Test <script>alert("test")</script>';
      renderComponent({templateName: specialName});

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText(specialName)).toBeInTheDocument();
    });

    it('handles different date formats', async () => {
      const user = userEvent.setup();
      renderComponent({createdOn: '2023-12-25T10:30:00Z'});

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(screen.getByText('Created on: 25 Dec 2023')).toBeInTheDocument();
    });
  });

  describe('Modal Structure and Classes', () => {
    it('applies correct CSS classes to modal', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const modal = document.querySelector('.archive-template-modal');
      expect(modal).toBeInTheDocument();
    });

    it('renders modal header correctly', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const header = document.querySelector('.modal-header');
      expect(header).toBeInTheDocument();
      expect(header).toHaveTextContent('Archive Template');
    });

    it('renders alert message with correct styling', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const alertLarge = document.querySelector('.alert-large');
      expect(alertLarge).toBeInTheDocument();

      const alertText = document.querySelector('.alert-text');
      expect(alertText).toBeInTheDocument();
    });

    it('renders template detail card structure', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      expect(
        document.querySelector('.template-detail-card'),
      ).toBeInTheDocument();
      expect(document.querySelector('.mostly-used-card')).toBeInTheDocument();
      expect(document.querySelector('.card-header')).toBeInTheDocument();
      expect(document.querySelector('.card-footer')).toBeInTheDocument();
    });

    it('renders button group with correct variants', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const cancelButton = screen.getByText('Cancel');
      const archiveButton = screen.getByText('Move to Archive');

      expect(cancelButton).toBeInTheDocument();
      expect(archiveButton).toBeInTheDocument();

      // Check button classes (Bootstrap variants)
      expect(cancelButton.closest('button')).toHaveClass('btn-secondary');
      expect(archiveButton.closest('button')).toHaveClass('btn-primary');
    });
  });

  describe('Accessibility', () => {
    it('modal is centered', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const modal = document.querySelector('.modal-dialog');
      expect(modal).toHaveClass('modal-dialog-centered');
    });

    it('has proper modal structure for screen readers', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const modal = document.querySelector('[role="dialog"]');
      expect(modal).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles rapid clicking of trigger', async () => {
      const user = userEvent.setup();
      renderComponent();

      const trigger = screen.getByText('Archive Template');

      // Click multiple times rapidly
      await user.click(trigger);
      await user.click(trigger);
      await user.click(trigger);

      // Modal should be open after rapid clicking
      expect(
        screen.getByText(
          'Do you really want to Archive this template? This action is not revertible.',
        ),
      ).toBeInTheDocument();
    });

    it('handles clicking archive button multiple times', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const archiveButton = screen.getByText('Move to Archive');

      // Click archive button multiple times rapidly
      await user.click(archiveButton);

      // Wait for the first call to complete
      await waitFor(() => {
        expect(mockMarkTemplateAsArchived).toHaveBeenCalledTimes(1);
      });

      // Modal should be closed, so we can't click the button again
      await waitFor(() => {
        expect(screen.queryByText('Move to Archive')).not.toBeInTheDocument();
      });
    });

    it('handles multiple archive button clicks by closing modal', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const archiveButton = screen.getByText('Move to Archive');

      // Click archive button once
      await user.click(archiveButton);

      // Modal should close, making the button unavailable for further clicks
      await waitFor(() => {
        expect(screen.queryByText('Move to Archive')).not.toBeInTheDocument();
      });

      // Service should have been called once
      expect(mockMarkTemplateAsArchived).toHaveBeenCalledTimes(1);
    });

    it('shows loading state during archive operation', async () => {
      const user = userEvent.setup();
      // Make the service call take some time
      mockMarkTemplateAsArchived.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100)),
      );

      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const archiveButton = screen.getByText('Move to Archive');

      // Click archive button
      await user.click(archiveButton);

      // Should show loading state
      expect(screen.getByText('Archiving...')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeDisabled();
      expect(screen.getByText('Archiving...')).toBeDisabled();

      // Wait for operation to complete
      await waitFor(() => {
        expect(screen.queryByText('Archiving...')).not.toBeInTheDocument();
      });
    });

    it('prevents multiple simultaneous archive operations', async () => {
      const user = userEvent.setup();
      let resolveArchive: (() => void) | undefined;
      const archivePromise = new Promise<void>(resolve => {
        resolveArchive = resolve;
      });

      mockMarkTemplateAsArchived.mockImplementation(() => archivePromise);

      renderComponent();

      // Open modal
      const trigger = screen.getByText('Archive Template');
      await user.click(trigger);

      const archiveButton = screen.getByText('Move to Archive');

      // Click archive button multiple times quickly
      await user.click(archiveButton);
      await user.click(archiveButton);
      await user.click(archiveButton);

      // Should only be called once
      expect(mockMarkTemplateAsArchived).toHaveBeenCalledTimes(1);

      // Resolve the promise to complete the test
      if (resolveArchive) {
        resolveArchive();
      }

      await waitFor(() => {
        expect(screen.queryByText('Archiving...')).not.toBeInTheDocument();
      });
    });
  });
});
