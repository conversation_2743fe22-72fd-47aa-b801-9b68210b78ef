import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {BasicDetails} from '../../../src/pages/CreateRA/BasicDetails';
import {TemplateForm} from '../../../src/types/template';
import {RiskForm} from '../../../src/types/risk';
import {TemplateFormStatus} from '../../../src/enums';
import {useDataStoreContext} from '../../../src/context';

// Mock dependencies
jest.mock('../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../src/components/InputComponent', () => ({
  InputComponent: function MockInputComponent({
    label,
    name,
    value,
    onChange,
    onBlur,
    form,
    maxLength,
    placeholder,
    helpText,
  }: any) {
    const fieldValue = value || form?.[name] || '';
    const isEmpty = !fieldValue || fieldValue.toString().trim() === '';
    const isInvalid = isEmpty;

    return (
      <div data-testid={`input-${name}`}>
        <label htmlFor={name}>{label}</label>
        <input
          id={name}
          name={name}
          value={fieldValue}
          onChange={onChange}
          onBlur={onBlur}
          className={`form-control ${isInvalid ? 'is-invalid' : ''}`}
          maxLength={maxLength}
          placeholder={placeholder}
        />
        {isInvalid && (
          <div className="invalid-feedback">
            This is a mandatory field. Please fill to process.
          </div>
        )}
        {helpText && (
          <small className="text-muted fs-14 form-text">{helpText}</small>
        )}
      </div>
    );
  },
}));

jest.mock('../../../src/components/DropdownTypeahead', () => {
  return function MockDropdownTypeahead({
    label,
    options,
    selected,
    onChange,
    multiple,
    isInvalid,
    errorMessage,
    useCheckboxes,
    onBlur,
  }: any) {
    return (
      <div
        data-testid={`dropdown-${label
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[()]/g, '')}`}
      >
        <label>{label}</label>
        <select
          data-testid={`select-${label
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[()]/g, '')}`}
          multiple={multiple}
          onChange={e => {
            const value = multiple
              ? Array.from(e.target.selectedOptions, (option: any) => ({
                  value: parseInt(option.value),
                  label: option.text,
                }))
              : options.find(
                  (opt: any) => opt.value === parseInt(e.target.value),
                );
            onChange(value);
          }}
          onBlur={onBlur}
          className={isInvalid ? 'is-invalid' : ''}
        >
          {!multiple && <option value="">Select...</option>}
          {options.map((option: any) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {isInvalid && errorMessage && (
          <div className="invalid-feedback">{errorMessage}</div>
        )}
      </div>
    );
  };
});

jest.mock('../../../src/components/SingleVesselOfficeDropdown', () => {
  return function MockSingleVesselOfficeDropdown({
    label,
    options,
    selected,
    onChange,
    isInvalid,
    errorMessage,
    onBlur,
  }: any) {
    return (
      <div data-testid="single-vessel-office-dropdown">
        <label>{label}</label>
        <select
          data-testid="vessel-office-select"
          onChange={e => {
            const selectedOption = options
              .flatMap((group: any) => group.options)
              .find((opt: any) => opt.value === e.target.value);
            onChange(selectedOption);
          }}
          onBlur={onBlur}
          className={isInvalid ? 'is-invalid' : ''}
        >
          <option value="">Select...</option>
          {options.map((group: any) => (
            <optgroup key={group.label} label={group.label}>
              {group.options.map((option: any) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </optgroup>
          ))}
        </select>
        {isInvalid && errorMessage && (
          <div className="invalid-feedback">{errorMessage}</div>
        )}
      </div>
    );
  };
});

jest.mock('../../../src/components/CustomDatePicker', () => {
  return function MockCustomDatePicker({
    label,
    value,
    onChange,
    isInvalid,
    errorMsg,
    onBlur,
  }: any) {
    return (
      <div data-testid="custom-date-picker">
        <label>{label}</label>
        <input
          type="date"
          data-testid="date-input"
          value={value ? value.toISOString().split('T')[0] : ''}
          onChange={e =>
            onChange(e.target.value ? new Date(e.target.value) : undefined)
          }
          onBlur={onBlur}
          className={isInvalid ? 'is-invalid' : ''}
        />
        {isInvalid && errorMsg && (
          <div className="invalid-feedback">{errorMsg}</div>
        )}
      </div>
    );
  };
});

jest.mock('../../../src/utils/helper', () => ({
  formatDateToYYYYMMDD: jest.fn((date: Date) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }),
  createGroupedVesselOfficeOptions: jest.fn(() => [
    {
      label: 'Vessels',
      options: [
        {value: '1', label: 'Vessel 1', vesselId: 101},
        {value: '2', label: 'Vessel 2', vesselId: 102},
      ],
    },
    {
      label: 'Offices',
      options: [
        {value: '3', label: 'Office 1'},
        {value: '4', label: 'Office 2'},
      ],
    },
  ]),
  findSelectedVesselOfficeOption: jest.fn(),
}));

jest.mock('../../../src/utils/common', () => ({
  vesselStatusAndLabelName: {
    1: 'Active',
    2: 'Inactive',
  },
}));

describe('BasicDetails', () => {
  const mockUseDataStoreContext = useDataStoreContext as jest.MockedFunction<
    typeof useDataStoreContext
  >;

  const mockDataStore = {
    riskCategoryList: [],
    hazardsList: [],
    riskParameterType: [],
    riskParameterList: [],
    taskReliabilityAssessList: [],
    riskParameterListForRiskRaiting: [],
    vesselListForRisk: [
      {id: 1, name: 'Vessel 1', code: 'V001'},
      {id: 2, name: 'Vessel 2', code: 'V002'},
    ],
    officeListForRisk: [
      {id: 1, name: 'Office 1'},
      {id: 2, name: 'Office 2'},
    ],
    approversReqListForRiskOffice: [
      {id: 1, name: 'Office Approver 1'},
      {id: 2, name: 'Office Approver 2'},
    ],
    approversReqListForRiskVessel: [
      {id: 3, name: 'Vessel Approver 1'},
      {id: 4, name: 'Vessel Approver 2'},
    ],
    crewMembersListForRisk: [],
  };

  const mockTemplateForm: TemplateForm = {
    task_requiring_ra: 'Test Task',
    task_duration: '2 days',
    task_alternative_consideration: 'Test alternative',
    task_rejection_reason: 'Test reason',
    worst_case_scenario: 'Test scenario',
    recovery_measures: 'Test measures',
    status: TemplateFormStatus.DRAFT,
    template_category: {
      category_id: [],
      is_other: false,
      value: '',
    },
    template_hazard: {
      is_other: false,
      value: '',
      hazard_id: [],
    },
    parameters: [],
    template_job: [],
    template_task_reliability_assessment: [],
    template_keyword: [],
  };

  const mockRiskForm: RiskForm = {
    task_requiring_ra: 'Risk Task',
    assessor: 1,
    vessel_ownership_id: 1,
    vessel_id: 101,
    date_risk_assessment: '2024-01-15',
    task_duration: '3 days',
    task_alternative_consideration: 'Risk alternative',
    task_rejection_reason: 'Risk reason',
    worst_case_scenario: 'Risk scenario',
    recovery_measures: 'Risk measures',
    status: 'draft',
    approval_required: [1, 2],
    risk_team_member: [],
    risk_category: {
      is_other: false,
      category_id: [],
      value: '',
    },
    risk_hazard: {
      is_other: false,
      hazard_id: [],
      value: '',
    },
    parameters: [],
    risk_job: [],
    risk_task_reliability_assessment: [],
  };

  const mockSetForm = jest.fn();
  const mockOnValidate = jest.fn();
  const mockRef = React.createRef<any>();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDataStoreContext.mockReturnValue({
      dataStore: mockDataStore as any,
      setDataStore: jest.fn(),
      roleConfig: {
        user: {user_id: 'test-user'} as any,
        riskAssessment: {hasPermision: true, canCreateNewTemplate: true},
      },
      ga4EventTrigger: jest.fn(),
    });
  });

  describe('Template Form Rendering', () => {
    it('renders basic template form fields', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
      expect(screen.getByLabelText('Duration of Task')).toBeInTheDocument();
      expect(
        screen.getByLabelText('Alternative Considered to carry out above task'),
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText('Reason for Rejecting Alternatives'),
      ).toBeInTheDocument();
    });

    it('handles input changes for template form', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: 'Updated Task'},
      });

      expect(mockSetForm).toHaveBeenCalledWith({
        ...mockTemplateForm,
        task_requiring_ra: 'Updated Task',
      });
    });

    it('handles blur events and sets touched state', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

      // Validation should be called
      expect(mockOnValidate).toHaveBeenCalled();
    });

    it('validates required fields for template form', () => {
      const emptyForm = {
        ...mockTemplateForm,
        task_requiring_ra: '',
        task_duration: '',
        task_alternative_consideration: '',
        task_rejection_reason: '',
      };

      render(
        <BasicDetails
          form={emptyForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      // Trigger validation by calling the ref method
      if (mockRef.current) {
        const isValid = mockRef.current.validate();
        expect(isValid).toBe(false);
      }
    });

    it('passes validation for complete template form', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      // Trigger validation by calling the ref method
      if (mockRef.current) {
        const isValid = mockRef.current.validate();
        expect(isValid).toBe(true);
      }
    });
  });

  describe('Risk Form Rendering', () => {
    it('renders risk form with additional fields', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        expect(
          screen.getByLabelText('Task Requiring R.A.'),
        ).toBeInTheDocument();
        expect(screen.getByLabelText('Duration of Task')).toBeInTheDocument();
        expect(screen.getByTestId('dropdown-assessor')).toBeInTheDocument();
        expect(
          screen.getByTestId('single-vessel-office-dropdown'),
        ).toBeInTheDocument();
        expect(screen.getByTestId('custom-date-picker')).toBeInTheDocument();
        expect(
          screen.getByTestId('dropdown-on-vessel-approvals-optional'),
        ).toBeInTheDocument();
      });
    });

    it('handles assessor dropdown changes', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        const assessorSelect = screen.getByTestId('select-assessor');
        fireEvent.change(assessorSelect, {target: {value: '2'}});
      });

      expect(mockSetForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        assessor: 2,
      });
    });

    it('handles vessel/office dropdown changes', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        const vesselOfficeSelect = screen.getByTestId('vessel-office-select');
        fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
      });

      expect(mockSetForm).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockRiskForm,
          vessel_ownership_id: 1,
          vessel_id: 101,
          office_id: expect.any(Number),
          office_name: expect.any(String),
        }),
      );
    });

    it('handles date changes', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        const dateInput = screen.getByTestId('date-input');
        fireEvent.change(dateInput, {target: {value: '2024-02-15'}});
      });

      expect(mockSetForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        date_risk_assessment: '2024-02-15',
      });
    });

    it('handles approval required changes', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        const approvalSelect = screen.getByTestId(
          'select-on-vessel-approvals-optional',
        );
        fireEvent.change(approvalSelect, {target: {value: ['1']}});
      });

      expect(mockSetForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        approval_required: [1],
      });
    });

    it('validates required fields for risk form', async () => {
      const emptyRiskForm = {
        ...mockRiskForm,
        task_requiring_ra: '',
        task_duration: '',
        task_alternative_consideration: '',
        task_rejection_reason: '',
        assessor: 0,
        vessel_ownership_id: 0,
        date_risk_assessment: '',
        approval_required: [],
      };

      render(
        <BasicDetails
          form={emptyRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        if (mockRef.current) {
          const isValid = mockRef.current.validate();
          expect(isValid).toBe(false);
        }
      });
    });

    it('passes validation for complete risk form', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        if (mockRef.current) {
          const isValid = mockRef.current.validate();
          expect(isValid).toBe(true);
        }
      });
    });
  });

  describe('Validation Logic', () => {
    it('validates individual fields correctly', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');

      // Clear the field to trigger validation
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: ''},
      });
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('calls onValidate callback when validation state changes', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: 'Valid Task'},
      });

      expect(mockOnValidate).toHaveBeenCalled();
    });

    it('handles validation without onValidate callback', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          type="template"
          ref={mockRef}
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: 'Valid Task'},
      });

      // Should not throw error when onValidate is not provided
      expect(() => {
        if (mockRef.current) {
          mockRef.current.validate();
        }
      }).not.toThrow();
    });
  });

  describe('Form Handlers', () => {
    it('handles dropdown changes correctly', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        const assessorSelect = screen.getByTestId('select-assessor');
        fireEvent.change(assessorSelect, {target: {value: '1'}});
      });

      expect(mockSetForm).toHaveBeenCalledWith(
        expect.objectContaining({
          assessor: 1,
        }),
      );
    });

    it('handles blur events for dropdowns', async () => {
      render(
        <BasicDetails
          form={mockRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        const assessorSelect = screen.getByTestId('select-assessor');
        fireEvent.blur(assessorSelect);
      });

      // The blur event should set touched state, validation happens on change
      expect(screen.getByTestId('select-assessor')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined form values gracefully', () => {
      const undefinedForm = {
        ...mockTemplateForm,
        task_requiring_ra: undefined as any,
        task_duration: undefined as any,
      };

      render(
        <BasicDetails
          form={undefinedForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      // The InputComponent mock should handle undefined values by showing empty string
      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      const durationInput = screen.getByLabelText('Duration of Task');

      expect(taskInput).toBeInTheDocument();
      expect(durationInput).toBeInTheDocument();
    });

    it('handles null values in risk form', async () => {
      const nullRiskForm = {
        ...mockRiskForm,
        assessor: null as any,
        vessel_ownership_id: null as any,
        date_risk_assessment: null as any,
      };

      render(
        <BasicDetails
          form={nullRiskForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        if (mockRef.current) {
          const isValid = mockRef.current.validate();
          expect(isValid).toBe(false);
        }
      });
    });

    it('handles empty approval_required array', async () => {
      const emptyApprovalForm = {...mockRiskForm, approval_required: []};

      render(
        <BasicDetails
          form={emptyApprovalForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        if (mockRef.current) {
          const isValid = mockRef.current.validate();
          expect(isValid).toBe(false);
        }
      });
    });

    it('handles switching between assessor types', async () => {
      const {rerender} = render(
        <BasicDetails
          form={{...mockRiskForm, assessor: 1}}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        expect(
          screen.getByTestId('dropdown-on-vessel-approvals-optional'),
        ).toBeInTheDocument();
      });

      rerender(
        <BasicDetails
          form={{...mockRiskForm, assessor: 2}}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="risk"
          ref={mockRef}
        />,
      );

      await waitFor(() => {
        expect(
          screen.getByTestId('dropdown-on-vessel-approvals-optional'),
        ).toBeInTheDocument();
      });
    });

    it('handles template type with default props', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          ref={mockRef}
        />,
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
      expect(screen.getByLabelText('Duration of Task')).toBeInTheDocument();
      // Should not render risk-specific fields
      expect(screen.queryByTestId('dropdown-assessor')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels and controls', () => {
      render(
        <BasicDetails
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      const durationInput = screen.getByLabelText('Duration of Task');
      const alternativeInput = screen.getByLabelText(
        'Alternative Considered to carry out above task',
      );
      const rejectionInput = screen.getByLabelText(
        'Reason for Rejecting Alternatives',
      );

      expect(taskInput).toHaveAttribute('name', 'task_requiring_ra');
      expect(durationInput).toHaveAttribute('name', 'task_duration');
      expect(alternativeInput).toHaveAttribute(
        'name',
        'task_alternative_consideration',
      );
      expect(rejectionInput).toHaveAttribute('name', 'task_rejection_reason');
    });

    it('shows proper error messages with invalid feedback', () => {
      const emptyForm = {
        ...mockTemplateForm,
        task_requiring_ra: '',
        task_duration: '',
        task_alternative_consideration: '',
        task_rejection_reason: '',
      };

      render(
        <BasicDetails
          form={emptyForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
          ref={mockRef}
        />,
      );

      // Trigger validation
      if (mockRef.current) {
        mockRef.current.validate();
      }

      const errorMessages = screen.getAllByText(
        'This is a mandatory field. Please fill to process.',
      );
      expect(errorMessages.length).toBeGreaterThan(0);
    });
  });

  describe('Performance', () => {
    it('does not cause unnecessary re-renders', () => {
      const renderSpy = jest.fn();
      const TestComponent = React.forwardRef((props: any, ref) => {
        renderSpy();
        return <BasicDetails {...props} ref={ref} />;
      });

      const {rerender} = render(
        <TestComponent
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
        />,
      );

      const initialRenderCount = renderSpy.mock.calls.length;

      // Re-render with same props
      rerender(
        <TestComponent
          form={mockTemplateForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
          type="template"
        />,
      );

      // Should have rendered twice (initial + rerender)
      expect(renderSpy.mock.calls.length).toBe(initialRenderCount + 1);
    });
  });
});
