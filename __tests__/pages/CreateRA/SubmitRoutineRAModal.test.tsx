import React from 'react';
import {screen, fireEvent, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {render} from '../../utils/test-utils';
import SubmitRoutineRAModal from '../../../src/pages/CreateRA/SubmitRoutineRAModal';
import {toast} from 'react-toastify';
import {getErrorMessage} from '../../../src/utils/common';

// Mock dependencies
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('../../../src/utils/common', () => ({
  getErrorMessage: jest.fn(),
}));

jest.mock('../../../src/components/CustomDatePicker', () => {
  return function MockCustomDatePicker(props: any) {
    return (
      <div data-testid="custom-date-picker">
        <label>{props.label}</label>
        <input
          data-testid="date-input"
          value={props.value ? props.value.toISOString().split('T')[0] : ''}
          onChange={(e) => {
            const date = e.target.value ? new Date(e.target.value) : undefined;
            props.onChange(date);
          }}
          placeholder={props.placeholder}
          required={props.isRequired}
        />
        {props.errorMsg && <div data-testid="error-message">{props.errorMsg}</div>}
      </div>
    );
  };
});

const mockToast = toast as jest.Mocked<typeof toast>;
const mockGetErrorMessage = getErrorMessage as jest.MockedFunction<typeof getErrorMessage>;

describe('SubmitRoutineRAModal', () => {
  const mockOnConfirm = jest.fn();
  const mockTrigger = <button data-testid="trigger-button">Open Modal</button>;

  const defaultProps = {
    onConfirm: mockOnConfirm,
    trigger: mockTrigger,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetErrorMessage.mockReturnValue('Mocked error message');
  });

  describe('Component Rendering', () => {
    it('renders trigger element correctly', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      expect(screen.getByTestId('trigger-button')).toBeInTheDocument();
      expect(screen.getByText('Open Modal')).toBeInTheDocument();
    });

    it('does not show modal initially', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      expect(screen.queryByText('Submitting Routine RA')).not.toBeInTheDocument();
    });

    it('shows modal when trigger is clicked', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      expect(screen.getByText('Submitting Routine RA')).toBeInTheDocument();
    });

    it('renders modal content correctly when open', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      expect(screen.getByText('Submitting Routine RA')).toBeInTheDocument();
      expect(screen.getByText('Do you want to submit this Routine Risk Assessment?')).toBeInTheDocument();
      expect(screen.getByText(/Submitting this would mean auto approval/)).toBeInTheDocument();
      expect(screen.getByTestId('custom-date-picker')).toBeInTheDocument();
      expect(screen.getByText('Approval Date')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Confirm')).toBeInTheDocument();
    });
  });

  describe('Modal Interactions', () => {
    it('closes modal when Cancel button is clicked', async () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);

      fireEvent.click(screen.getByTestId('trigger-button'));
      expect(screen.getByText('Submitting Routine RA')).toBeInTheDocument();

      fireEvent.click(screen.getByText('Cancel'));

      await waitFor(() => {
        expect(screen.queryByText('Submitting Routine RA')).not.toBeInTheDocument();
      });
    });

    it('resets approval date when modal is closed', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      // Set a date
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      // Close modal
      fireEvent.click(screen.getByText('Cancel'));
      
      // Reopen modal
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      // Check that date is reset
      const newDateInput = screen.getByTestId('date-input');
      expect(newDateInput.value).toBe('');
    });

    it('disables Confirm button when no approval date is selected', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const confirmButton = screen.getByText('Confirm');
      expect(confirmButton).toBeDisabled();
    });

    it('enables Confirm button when approval date is selected', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      const confirmButton = screen.getByText('Confirm');
      expect(confirmButton).not.toBeDisabled();
    });
  });

  describe('Date Selection', () => {
    it('updates approval date when date is selected', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      expect(dateInput.value).toBe('2023-12-25');
    });

    it('clears approval date when date is cleared', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      fireEvent.change(dateInput, { target: { value: '' } });
      
      expect(dateInput.value).toBe('');
    });
  });

  describe('Form Submission', () => {
    it('calls onConfirm with correct parameters when form is submitted', async () => {
      mockOnConfirm.mockResolvedValue({ message: 'Success!' });
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      await waitFor(() => {
        expect(mockOnConfirm).toHaveBeenCalledWith({
          approveDate: new Date('2023-12-25'),
        });
      });
    });

    it('shows success toast with custom message when onConfirm returns message', async () => {
      const customMessage = 'Custom success message';
      mockOnConfirm.mockResolvedValue({ message: customMessage });
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith(customMessage);
      });
    });

    it('shows default success toast when onConfirm returns no message', async () => {
      mockOnConfirm.mockResolvedValue({});
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('RA approved successfully.');
      });
    });

    it('shows default success toast when onConfirm returns undefined', async () => {
      mockOnConfirm.mockResolvedValue(undefined);
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('RA approved successfully.');
      });
    });

    it('closes modal after successful submission', async () => {
      mockOnConfirm.mockResolvedValue({ message: 'Success!' });
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      await waitFor(() => {
        expect(screen.queryByText('Submitting Routine RA')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('shows error toast when onConfirm throws an error', async () => {
      const error = new Error('API Error');
      mockOnConfirm.mockRejectedValue(error);
      mockGetErrorMessage.mockReturnValue('Custom error message');
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      await waitFor(() => {
        expect(mockGetErrorMessage).toHaveBeenCalledWith(
          error,
          'Failed to approve the RA. Please try again later.'
        );
        expect(mockToast.error).toHaveBeenCalledWith('Custom error message');
      });
    });

    it('closes modal after error', async () => {
      const error = new Error('API Error');
      mockOnConfirm.mockRejectedValue(error);
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      await waitFor(() => {
        expect(screen.queryByText('Submitting Routine RA')).not.toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('disables buttons during submission', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockOnConfirm.mockReturnValue(promise);
      
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const dateInput = screen.getByTestId('date-input');
      fireEvent.change(dateInput, { target: { value: '2023-12-25' } });
      
      fireEvent.click(screen.getByText('Confirm'));
      
      // Check buttons are disabled during loading
      expect(screen.getByText('Cancel')).toBeDisabled();
      expect(screen.getByText('Confirm')).toBeDisabled();
      
      // Resolve the promise
      resolvePromise!({ message: 'Success!' });
      
      await waitFor(() => {
        expect(screen.queryByText('Submitting Routine RA')).not.toBeInTheDocument();
      });
    });
  });

  describe('CustomDatePicker Props', () => {
    it('passes correct props to CustomDatePicker', () => {
      render(<SubmitRoutineRAModal {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('trigger-button'));
      
      const datePicker = screen.getByTestId('custom-date-picker');
      expect(datePicker).toBeInTheDocument();
      
      expect(screen.getByText('Approval Date')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Select Date')).toBeInTheDocument();
      expect(screen.getByTestId('date-input')).toHaveAttribute('required');
    });
  });
});
