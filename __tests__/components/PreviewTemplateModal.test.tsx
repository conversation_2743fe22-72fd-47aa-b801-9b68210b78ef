import React from 'react';
import {screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import {render, mockNavigate} from '../utils/test-utils';
import {PreviewTemplateModal} from '../../src/components/PreviewTemplateModal';
import {getTemplateById} from '../../src/services/services';
import {createFormFromData} from '../../src/utils/helper';
import {TemplateForm} from '../../src/types/template';
import {TemplateFormStatus} from '../../src/enums';

// Mock dependencies

jest.mock('../../src/services/services', () => ({
  getTemplateById: jest.fn(),
  getRiskCategoryList: jest.fn().mockResolvedValue([]),
  getHazardsList: jest.fn().mockResolvedValue([]),
  getRiskParameterType: jest.fn().mockResolvedValue([]),
  getTaskReliabilityAssessList: jest.fn().mockResolvedValue([]),
  getMainRiskParameterType: jest.fn().mockResolvedValue([]),
}));

jest.mock('../../src/utils/helper', () => ({
  createFormFromData: jest.fn(),
}));

jest.mock('../../src/components/Loader', () => {
  return function MockLoader({isOverlayLoader}: {isOverlayLoader?: boolean}) {
    return (
      <div data-testid="loader" data-overlay={isOverlayLoader}>
        Loading...
      </div>
    );
  };
});

jest.mock('../../src/pages/CreateRA/PreviewFormDetails', () => {
  return function MockPreviewFormDetails({
    form,
    setForm,
    type,
    previewOnly,
  }: {
    form: TemplateForm;
    setForm: (f: any) => void;
    type: string;
    previewOnly: boolean;
  }) {
    return (
      <div data-testid="preview-form-details">
        <div data-testid="form-task">{form.task_requiring_ra}</div>
        <div data-testid="form-type">{type}</div>
        <div data-testid="preview-only">{previewOnly.toString()}</div>
        <button
          onClick={() => setForm({...form, task_requiring_ra: 'Updated'})}
        >
          Update Form
        </button>
      </div>
    );
  };
});

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('PreviewTemplateModal', () => {
  const mockOnClose = jest.fn();
  const mockGetTemplateById = getTemplateById as jest.MockedFunction<
    typeof getTemplateById
  >;
  const mockCreateFormFromData = createFormFromData as jest.MockedFunction<
    typeof createFormFromData
  >;

  const defaultProps = {
    onClose: mockOnClose,
    id: 123,
    canUseTemplate: true,
  };

  const mockTemplateData = {
    result: {
      id: 123,
      task_requiring_ra: 'Test Template Task',
      task_duration: '2 hours',
      task_alternative_consideration: 'Test alternative',
      task_rejection_reason: 'Test rejection',
      worst_case_scenario: 'Test worst case',
      recovery_measures: 'Test recovery',
      template_category: [],
      template_hazards: [],
      template_job: [],
      template_task_reliability_assessment: [],
      template_keyword: ['test'],
    },
  };

  const mockFormData: TemplateForm = {
    task_requiring_ra: 'Test Template Task',
    task_duration: '2 hours',
    task_alternative_consideration: 'Test alternative',
    task_rejection_reason: 'Test rejection',
    worst_case_scenario: 'Test worst case',
    recovery_measures: 'Test recovery',
    status: TemplateFormStatus.DRAFT,
    template_category: {category_id: [], is_other: false, value: ''},
    template_hazard: {is_other: false, value: '', hazard_id: []},
    parameters: [],
    template_job: [],
    template_task_reliability_assessment: [],
    template_keyword: ['test'],
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    mockCreateFormFromData.mockReturnValue(mockFormData);
    mockGetTemplateById.mockResolvedValue(mockTemplateData);
  });

  describe('Basic Rendering', () => {
    it('renders modal with correct structure', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      // Check modal is rendered
      expect(screen.getByRole('dialog')).toBeInTheDocument();

      // Check modal header
      expect(screen.getByText('Template Preview')).toBeInTheDocument();

      // Check buttons
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Use Template')).toBeInTheDocument();

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Check PreviewFormDetails is rendered
      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
    });

    it('renders modal with correct props passed to PreviewFormDetails', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Check props passed to PreviewFormDetails
      expect(screen.getByTestId('form-task')).toHaveTextContent(
        'Test Template Task',
      );
      expect(screen.getByTestId('form-type')).toHaveTextContent('template');
      expect(screen.getByTestId('preview-only')).toHaveTextContent('true');
    });

    it('applies correct CSS classes to modal', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const modal = document.querySelector('.modal');
      expect(modal).toBeInTheDocument();

      const modalDialog = document.querySelector(
        '.top-modal.preview-template-modal',
      );
      expect(modalDialog).toBeInTheDocument();

      const modalBody = document.querySelector('.edit-modal-body');
      expect(modalBody).toBeInTheDocument();
    });
  });

  describe('Modal Properties', () => {
    it('renders modal with xl size', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const modalDialog = document.querySelector('.modal-xl');
      expect(modalDialog).toBeInTheDocument();
    });

    it('renders modal with static backdrop', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const modal = document.querySelector('.modal');
      expect(modal).toBeInTheDocument();
    });

    it('shows modal by default', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
      expect(modal).toBeVisible();
    });
  });

  describe('Loading States', () => {
    it('shows loader initially', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();
      expect(screen.getByTestId('loader')).toHaveAttribute(
        'data-overlay',
        'true',
      );
    });

    it('hides loader after data is fetched', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });

    it('shows loader during API call', async () => {
      // Make API call take longer
      mockGetTemplateById.mockImplementation(
        () =>
          new Promise(resolve =>
            setTimeout(() => resolve(mockTemplateData), 100),
          ),
      );

      render(<PreviewTemplateModal {...defaultProps} />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });
  });

  describe('Data Fetching', () => {
    it('calls getTemplateById with correct id on mount', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      expect(mockGetTemplateById).toHaveBeenCalledWith('123');
      expect(mockGetTemplateById).toHaveBeenCalledTimes(1);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });

    it('calls createFormFromData with fetched data', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(mockCreateFormFromData).toHaveBeenCalledWith(
          mockTemplateData.result,
        );
      });
    });

    it('does not fetch data when id is not provided', () => {
      render(<PreviewTemplateModal {...defaultProps} id={0} />);

      expect(mockGetTemplateById).not.toHaveBeenCalled();
    });

    it('refetches data when id changes', async () => {
      const {rerender} = render(
        <PreviewTemplateModal {...defaultProps} id={123} />,
      );

      await waitFor(() => {
        expect(mockGetTemplateById).toHaveBeenCalledWith('123');
      });

      rerender(<PreviewTemplateModal {...defaultProps} id={456} />);

      await waitFor(() => {
        expect(mockGetTemplateById).toHaveBeenCalledWith('456');
      });

      expect(mockGetTemplateById).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling', () => {
    it('handles API error gracefully', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      mockGetTemplateById.mockRejectedValue(new Error('API Error'));

      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching draft',
        expect.any(Error),
      );
      consoleErrorSpy.mockRestore();
    });

    it('still renders form with default data when API fails', async () => {
      mockGetTemplateById.mockRejectedValue(new Error('API Error'));

      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
    });
  });

  describe('Button Interactions', () => {
    it('calls onClose when Cancel button is clicked', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('navigates to correct route when Use Template button is clicked', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      const useTemplateButton = screen.getByText('Use Template');
      fireEvent.click(useTemplateButton);

      expect(mockNavigate).toHaveBeenCalledWith(
        '/risk-assessment/templates/123/risks/create',
      );
    });

    it('disables Use Template button when loading', () => {
      mockGetTemplateById.mockImplementation(
        () =>
          new Promise(resolve =>
            setTimeout(() => resolve(mockTemplateData), 1000),
          ),
      );

      render(<PreviewTemplateModal {...defaultProps} />);

      const useTemplateButton = screen.getByText('Use Template');
      expect(useTemplateButton).toBeDisabled();
    });

    it('enables Use Template button after loading completes', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      const useTemplateButton = screen.getByText('Use Template');
      expect(useTemplateButton).not.toBeDisabled();
    });
  });

  describe('canUseTemplate Prop', () => {
    it('shows Use Template button when canUseTemplate is true', async () => {
      render(<PreviewTemplateModal {...defaultProps} canUseTemplate={true} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Use Template')).toBeInTheDocument();
    });

    it('hides Use Template button when canUseTemplate is false', async () => {
      render(<PreviewTemplateModal {...defaultProps} canUseTemplate={false} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.queryByText('Use Template')).not.toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('defaults canUseTemplate to true when not provided', async () => {
      const {canUseTemplate, ...propsWithoutCanUseTemplate} = defaultProps;
      render(<PreviewTemplateModal {...propsWithoutCanUseTemplate} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Use Template')).toBeInTheDocument();
    });
  });

  describe('Form State Management', () => {
    it('updates form state when setForm is called', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Initial state
      expect(screen.getByTestId('form-task')).toHaveTextContent(
        'Test Template Task',
      );

      // Trigger form update
      const updateButton = screen.getByText('Update Form');
      fireEvent.click(updateButton);

      // Check updated state
      expect(screen.getByTestId('form-task')).toHaveTextContent('Updated');
    });

    it('initializes form with default data from createFormFromData', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      expect(mockCreateFormFromData).toHaveBeenCalledWith();
    });

    it('updates form with fetched template data', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(mockCreateFormFromData).toHaveBeenCalledWith(
          mockTemplateData.result,
        );
      });
    });
  });

  describe('Modal Header', () => {
    it('displays correct title', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Template Preview')).toBeInTheDocument();
    });
  });

  describe('PreviewFormDetails Integration', () => {
    it('passes correct props to PreviewFormDetails', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      const previewFormDetails = screen.getByTestId('preview-form-details');
      expect(previewFormDetails).toBeInTheDocument();

      // Check all props are passed correctly
      expect(screen.getByTestId('form-type')).toHaveTextContent('template');
      expect(screen.getByTestId('preview-only')).toHaveTextContent('true');
    });

    it('passes atRiskRef as null object', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // The atRiskRef should be passed as {current: null}
      // This is tested indirectly through the component rendering without errors
      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
    });

    it('passes empty functions for handlePreviewPublush and handleSaveToDraft', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // These functions should be empty and not cause errors
      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles undefined template data gracefully', async () => {
      mockGetTemplateById.mockResolvedValue({result: undefined});

      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(mockCreateFormFromData).toHaveBeenCalledWith(undefined);
      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
    });

    it('handles null template data gracefully', async () => {
      mockGetTemplateById.mockResolvedValue({result: null});

      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(mockCreateFormFromData).toHaveBeenCalledWith(null);
      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
    });

    it('handles very large id numbers', async () => {
      const largeId = 999999999;
      render(<PreviewTemplateModal {...defaultProps} id={largeId} />);

      expect(mockGetTemplateById).toHaveBeenCalledWith(largeId.toString());

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });

    it('handles negative id numbers', () => {
      const negativeId = -1;
      render(<PreviewTemplateModal {...defaultProps} id={negativeId} />);

      expect(mockGetTemplateById).toHaveBeenCalledWith(negativeId.toString());
    });

    it('handles string conversion of id correctly', async () => {
      const numericId = 123;
      render(<PreviewTemplateModal {...defaultProps} id={numericId} />);

      expect(mockGetTemplateById).toHaveBeenCalledWith('123');

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });
  });

  describe('User Interactions', () => {
    it('handles rapid button clicks gracefully', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      const cancelButton = screen.getByText('Cancel');

      // Rapid clicks
      fireEvent.click(cancelButton);
      fireEvent.click(cancelButton);
      fireEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalledTimes(3);
    });

    it('handles keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Focus the Cancel button directly and press Enter
      const cancelButton = screen.getByText('Cancel');
      cancelButton.focus();
      await user.keyboard('{Enter}');

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('handles modal close via onHide', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const modal = screen.getByRole('dialog');

      // Simulate modal onHide event (usually triggered by backdrop click or ESC)
      fireEvent.click(modal);

      // Note: The actual onHide behavior depends on react-bootstrap Modal implementation
      // This test ensures the modal structure is correct
      expect(modal).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
    });

    it('has focusable elements in correct order', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Check that buttons are focusable
      const cancelButton = screen.getByText('Cancel');
      const useTemplateButton = screen.getByText('Use Template');

      expect(cancelButton).toBeInTheDocument();
      expect(useTemplateButton).toBeInTheDocument();

      // Test that buttons can receive focus
      cancelButton.focus();
      expect(cancelButton).toHaveFocus();

      useTemplateButton.focus();
      expect(useTemplateButton).toHaveFocus();
    });

    it('maintains focus within modal', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Modal should trap focus within its boundaries
      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('does not cause unnecessary re-renders', async () => {
      const {rerender} = render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Clear previous calls
      mockGetTemplateById.mockClear();

      // Re-render with same props
      rerender(<PreviewTemplateModal {...defaultProps} />);

      // Should not fetch data again
      expect(mockGetTemplateById).not.toHaveBeenCalled();
    });

    it('cleans up properly on unmount', async () => {
      const {unmount} = render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Unmount component
      unmount();

      // Component should unmount without errors
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  describe('Function Coverage', () => {
    it('covers fetchTemplateData function with successful response', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      // Wait for the async function to complete
      await waitFor(() => {
        expect(mockGetTemplateById).toHaveBeenCalledWith('123');
        expect(mockCreateFormFromData).toHaveBeenCalledWith(
          mockTemplateData.result,
        );
      });

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });

    it('covers fetchTemplateData function with error handling', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      mockGetTemplateById.mockRejectedValueOnce(new Error('Network error'));

      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Error fetching draft',
          expect.any(Error),
        );
      });

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      consoleErrorSpy.mockRestore();
    });

    it('covers useEffect dependency array with id change', async () => {
      const {rerender} = render(
        <PreviewTemplateModal {...defaultProps} id={123} />,
      );

      await waitFor(() => {
        expect(mockGetTemplateById).toHaveBeenCalledWith('123');
      });

      mockGetTemplateById.mockClear();

      // Change id to trigger useEffect
      rerender(<PreviewTemplateModal {...defaultProps} id={456} />);

      await waitFor(() => {
        expect(mockGetTemplateById).toHaveBeenCalledWith('456');
      });
    });

    it('covers useEffect early return when id is falsy', () => {
      render(<PreviewTemplateModal {...defaultProps} id={0} />);

      expect(mockGetTemplateById).not.toHaveBeenCalled();
    });

    it('covers all button click handlers', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Test Cancel button onClick
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      expect(mockOnClose).toHaveBeenCalledTimes(1);

      // Test Use Template button onClick
      const useTemplateButton = screen.getByText('Use Template');
      fireEvent.click(useTemplateButton);
      expect(mockNavigate).toHaveBeenCalledWith(
        '/risk-assessment/templates/123/risks/create',
      );
    });

    it('covers modal onHide handler', () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      const modal = screen.getByRole('dialog');

      // Simulate onHide event (ESC key or backdrop click)
      fireEvent.keyDown(modal, {key: 'Escape', code: 'Escape'});

      // The modal should still be present as onHide is handled by react-bootstrap
      expect(modal).toBeInTheDocument();
    });

    it('covers setForm function calls', async () => {
      render(<PreviewTemplateModal {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      // Test that setForm is called during data fetching
      expect(mockCreateFormFromData).toHaveBeenCalled();

      // Test setForm through the mock component
      const updateButton = screen.getByText('Update Form');
      fireEvent.click(updateButton);

      expect(screen.getByTestId('form-task')).toHaveTextContent('Updated');
    });

    it('covers loading state transitions', async () => {
      // Mock a delayed response
      mockGetTemplateById.mockImplementation(
        () =>
          new Promise(resolve =>
            setTimeout(() => resolve(mockTemplateData), 50),
          ),
      );

      render(<PreviewTemplateModal {...defaultProps} />);

      // Initially loading
      expect(screen.getByTestId('loader')).toBeInTheDocument();

      // Wait for loading to complete
      await waitFor(
        () => {
          expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
        },
        {timeout: 1000},
      );
    });

    it('covers all conditional rendering paths', async () => {
      // Test with canUseTemplate false
      const {unmount} = render(
        <PreviewTemplateModal {...defaultProps} canUseTemplate={false} />,
      );

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.queryByText('Use Template')).not.toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();

      // Unmount first component to avoid conflicts
      unmount();

      // Test with canUseTemplate true (default)
      render(<PreviewTemplateModal {...defaultProps} canUseTemplate={true} />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Use Template')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });
  });
});
