import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {ExitCreateRiskPageModal} from '../../src/components/ExitCreateRiskPageModal';
import * as router from 'react-router-dom';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('ExitCreateRiskPageModal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render modal with correct content', () => {
    const mockHandleSubmit = jest.fn();
    render(<ExitCreateRiskPageModal onClose={mockOnClose} handleSubmit={mockHandleSubmit} />);

    // Check modal title
    expect(
      screen.getByText('Exit RA Creation without Saving'),
    ).toBeInTheDocument();

    // Check warning message
    expect(
      screen.getByText(
        /Are you sure you want to exit without saving this Risk Assessment as a draft\?/,
      ),
    ).toBeInTheDocument();
    expect(
      screen.getByText(/All entered information will be lost permanently\./),
    ).toBeInTheDocument();

    // Check buttons
    expect(screen.getByText('Discard RA')).toBeInTheDocument();
    expect(screen.getByText('Keep Editing')).toBeInTheDocument();
  });

  it('should call handleSubmit when "Discard RA" button is clicked', () => {
    const mockHandleSubmit = jest.fn();
    render(<ExitCreateRiskPageModal onClose={mockOnClose} handleSubmit={mockHandleSubmit} />);

    const discardButton = screen.getByText('Discard RA');
    fireEvent.click(discardButton);

    expect(mockHandleSubmit).toHaveBeenCalled();
  });

  it('should call onClose when "Keep Editing" button is clicked', () => {
    const mockHandleSubmit = jest.fn();
    render(<ExitCreateRiskPageModal onClose={mockOnClose} handleSubmit={mockHandleSubmit} />);

    const keepEditingButton = screen.getByText('Keep Editing');
    fireEvent.click(keepEditingButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should have correct data-testid for error alert', () => {
    const mockHandleSubmit = jest.fn();
    render(<ExitCreateRiskPageModal onClose={mockOnClose} handleSubmit={mockHandleSubmit} />);

    expect(screen.getByTestId('error-alert')).toBeInTheDocument();
  });
});
