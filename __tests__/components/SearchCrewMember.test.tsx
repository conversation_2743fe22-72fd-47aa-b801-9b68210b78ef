import React from "react";
import { render, fireEvent, screen, act, cleanup } from "@testing-library/react";
import Search<PERSON>rewMember from "../../src/components/SearchCrewMember";

const mockOptions = [
  { id: "1", full_name: "<PERSON>", subText: "<PERSON>" },
  { id: "2", full_name: "<PERSON>", subText: "Co-pilot" },
  { id: "3", full_name: "<PERSON>", subText: "Engineer" },
];

describe("SearchCrewMember", () => {
  afterEach(() => {
    cleanup();
  });

  it("renders input with placeholder", () => {
    render(
      <SearchCrewMember
        value={[]}
        options={mockOptions}
        onChange={jest.fn()}
        placeholder="Search crew"
      />
    );
    expect(screen.getByPlaceholderText("Search crew")).toBeInTheDocument();
  });

  it("renders with default placeholder when none provided", () => {
    render(
      <SearchCrewMember
        value={[]}
        options={mockOptions}
        onChange={jest.fn()}
      />
    );
    expect(screen.getByPlaceholderText("Select user")).toBeInTheDocument();
  });

  it("shows dropdown on input focus", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
    expect(screen.getByText("Charlie Brown")).toBeInTheDocument();
  });

  it("shows dropdown on wrapper focus", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const wrapper = screen.getByPlaceholderText("Select user").closest('.user-search-input-wrapper');
    fireEvent.focus(wrapper!);
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
  });

  it("filters users based on search input (full_name)", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: "bob" } });
    expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
    expect(screen.queryByText("Alice Smith")).not.toBeInTheDocument();
    expect(screen.queryByText("Charlie Brown")).not.toBeInTheDocument();
  });

  it("filters users based on search input (subText)", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: "pilot" } });
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
    expect(screen.queryByText("Charlie Brown")).not.toBeInTheDocument();
  });

  it("filters users case-insensitively", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: "ALICE" } });
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    expect(screen.queryByText("Bob Johnson")).not.toBeInTheDocument();
  });

  it("shows all users when search is empty", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: "alice" } });
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    expect(screen.queryByText("Bob Johnson")).not.toBeInTheDocument();

    // Clear search
    fireEvent.change(input, { target: { value: "" } });
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
    expect(screen.getByText("Charlie Brown")).toBeInTheDocument();
  });

  it('shows "No users found." if no match', () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: "zzz" } });
    expect(screen.getByText("No users found.")).toBeInTheDocument();
  });

  it("calls onChange and closes dropdown when user is selected", () => {
    const handleChange = jest.fn();
    render(
      <SearchCrewMember
        value={[]}
        options={mockOptions}
        onChange={handleChange}
      />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    const userButton = screen.getByText("Alice Smith").closest("button");
    expect(userButton).toBeInTheDocument();
    fireEvent.click(userButton!);
    expect(handleChange).toHaveBeenCalledWith(["1"]);
    expect(screen.queryByText("Alice Smith")).not.toBeInTheDocument();
  });

  it("clears search input when user is selected", () => {
    const handleChange = jest.fn();
    render(
      <SearchCrewMember
        value={[]}
        options={mockOptions}
        onChange={handleChange}
      />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: "alice" } });
    expect(input).toHaveValue("alice");

    const userButton = screen.getByText("Alice Smith").closest("button");
    fireEvent.click(userButton!);

    // Reopen dropdown to check if search was cleared
    fireEvent.focus(input);
    expect(input).toHaveValue("");
  });

  it("closes dropdown when clicking outside", () => {
    render(
      <div>
        <SearchCrewMember
          value={[]}
          options={mockOptions}
          onChange={jest.fn()}
        />
        <button data-testid="outside">Outside</button>
      </div>
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    fireEvent.mouseDown(screen.getByTestId("outside"));
    expect(screen.queryByText("Alice Smith")).not.toBeInTheDocument();
  });

  it("closes dropdown when clicking outside UserSelectorDropdown", () => {
    render(
      <div>
        <SearchCrewMember
          value={[]}
          options={mockOptions}
          onChange={jest.fn()}
        />
        <button data-testid="outside">Outside</button>
      </div>
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();

    // Click outside the UserSelectorDropdown specifically
    fireEvent.mouseDown(screen.getByTestId("outside"));
    expect(screen.queryByText("Alice Smith")).not.toBeInTheDocument();
  });

  it("handles empty options array", () => {
    render(
      <SearchCrewMember
        value={[]}
        options={[]}
        onChange={jest.fn()}
      />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("No users found.")).toBeInTheDocument();
  });

  it("opens dropdown when typing in search input", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");

    // Type in input should open dropdown
    fireEvent.change(input, { target: { value: "alice" } });
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
  });

  it("handles onSearch with null value", () => {
    const handleChange = jest.fn();
    render(
      <SearchCrewMember
        value={[]}
        options={mockOptions}
        onChange={handleChange}
      />
    );
    const input = screen.getByPlaceholderText("Select user");

    // Simulate SearchInput calling onSearch with null
    fireEvent.change(input, { target: { value: "" } });
    fireEvent.focus(input);

    // Should show all options when search is empty
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
    expect(screen.getByText("Charlie Brown")).toBeInTheDocument();
  });

  it("displays user initials correctly", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);

    // Check that initials are displayed (getInitials function is called)
    const avatars = document.querySelectorAll('.avatar');
    expect(avatars).toHaveLength(3);
    expect(avatars[0]).toHaveTextContent('AS'); // Alice Smith
    expect(avatars[1]).toHaveTextContent('BJ'); // Bob Johnson
    expect(avatars[2]).toHaveTextContent('CB'); // Charlie Brown
  });

  it("displays user details correctly", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);

    expect(screen.getByText("Pilot")).toBeInTheDocument();
    expect(screen.getByText("Co-pilot")).toBeInTheDocument();
    expect(screen.getByText("Engineer")).toBeInTheDocument();
  });

  it("handles user selection with different user IDs", () => {
    const handleChange = jest.fn();
    render(
      <SearchCrewMember
        value={[]}
        options={mockOptions}
        onChange={handleChange}
      />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);

    // Select Bob Johnson (id: "2")
    const bobButton = screen.getByText("Bob Johnson").closest("button");
    fireEvent.click(bobButton!);
    expect(handleChange).toHaveBeenCalledWith(["2"]);
  });

  it("cleans up event listeners on unmount", () => {
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
    const { unmount } = render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );

    unmount();
    expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));
    removeEventListenerSpy.mockRestore();
  });

  it("handles options with default empty array", () => {
    // Test the default value for options prop in UserSelectorDropdown
    const handleChange = jest.fn();
    render(
      <SearchCrewMember
        value={[]}
        options={[]}
        onChange={handleChange}
      />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("No users found.")).toBeInTheDocument();
  });

  it("maintains dropdown state correctly", () => {
    render(
      <SearchCrewMember value={[]} options={mockOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");

    // Initially dropdown should be closed
    expect(screen.queryByText("Alice Smith")).not.toBeInTheDocument();

    // Open dropdown
    fireEvent.focus(input);
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();

    // Close dropdown by clicking outside
    fireEvent.mouseDown(document.body);
    expect(screen.queryByText("Alice Smith")).not.toBeInTheDocument();
  });

  it("handles search input with special characters", () => {
    const specialOptions = [
      { id: "1", full_name: "Alice O'Connor", subText: "Pilot" },
      { id: "2", full_name: "Bob-Smith", subText: "Co-pilot" },
    ];

    render(
      <SearchCrewMember value={[]} options={specialOptions} onChange={jest.fn()} />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: "o'connor" } });
    expect(screen.getByText("Alice O'Connor")).toBeInTheDocument();
    expect(screen.queryByText("Bob-Smith")).not.toBeInTheDocument();
  });

  it("handles value prop correctly", () => {
    // Test that value prop is passed but not used in this component's display logic
    render(
      <SearchCrewMember
        value={["1", "2"]}
        options={mockOptions}
        onChange={jest.fn()}
      />
    );
    const input = screen.getByPlaceholderText("Select user");
    expect(input).toBeInTheDocument();
    // The value prop doesn't affect the display in this component
  });

  it("handles click inside dropdown (should not close)", () => {
    render(
      <SearchCrewMember
        value={[]}
        options={mockOptions}
        onChange={jest.fn()}
      />
    );

    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();

    // Click inside the dropdown should not close it
    const dropdown = document.querySelector('.user-selector-dropdown');
    if (dropdown) {
      fireEvent.mouseDown(dropdown);
      expect(screen.getByText("Alice Smith")).toBeInTheDocument();
    }
  });



  it("tests UserSelectorDropdown with undefined options (default parameter)", () => {
    // This test ensures the default parameter options = [] is covered
    render(
      <SearchCrewMember
        value={[]}
        options={[]} // Empty array to test the "No users found" branch
        onChange={jest.fn()}
      />
    );
    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("No users found.")).toBeInTheDocument();
  });

  it("covers all branches in event target checking", () => {
    render(
      <div>
        <SearchCrewMember
          value={[]}
          options={mockOptions}
          onChange={jest.fn()}
        />
        <div data-testid="outside-div">Outside Div</div>
      </div>
    );

    const input = screen.getByPlaceholderText("Select user");
    fireEvent.focus(input);
    expect(screen.getByText("Alice Smith")).toBeInTheDocument();

    // Test clicking outside using fireEvent.mouseDown which properly simulates the event
    const outsideDiv = screen.getByTestId("outside-div");
    fireEvent.mouseDown(outsideDiv);

    expect(screen.queryByText("Alice Smith")).not.toBeInTheDocument();
  });
});
