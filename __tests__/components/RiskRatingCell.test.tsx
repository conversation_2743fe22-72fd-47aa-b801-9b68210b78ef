import React from 'react';
import {render, screen} from '@testing-library/react';
import {RiskRatingCell} from '../../src/components/RiskRatingCell';

// Mock getRiskLabel and getCellColor
jest.mock('../../src/pages/CreateRA/AddJobsStep', () => ({
  getRiskLabel: (rating: string) => `Label-${rating}`,
}));
jest.mock('../../src/components/InitialRiskRatingModal', () => ({
  getCellColor: (rating: string) => `color-${rating}`,
}));

describe('RiskRatingCell', () => {
  it('renders all parameter labels (P, E, A, R)', () => {
    render(<RiskRatingCell rating={[]} />);
    expect(screen.getByText('P')).toBeInTheDocument();
    expect(screen.getByText('E')).toBeInTheDocument();
    expect(screen.getByText('A')).toBeInTheDocument();
    expect(screen.getByText('R')).toBeInTheDocument();
  });

  it('shows "Not Selected" when no rating is provided', () => {
    render(<RiskRatingCell rating={[]} />);
    const buttons = screen.getAllByRole('button');
    buttons.forEach(btn => {
      expect(btn).toHaveTextContent('Not Selected');
      expect(btn).toHaveStyle({
        background: '#F6F8FA',
        color: '#333333',
        border: '1px solid #e0e0e0',
      });
    });
  });

  it('renders correct risk labels and styles for provided ratings', () => {
    const rating = [
      {parameter_type_id: 1, rating: 'HIGH-1'},
      {parameter_type_id: 2, rating: 'MEDIUM-2'},
      {parameter_type_id: 3, rating: 'LOW-3'},
      // parameter_type_id: 4 is missing
    ];
    render(<RiskRatingCell rating={rating} />);
    // P
    expect(screen.getByText('Label-HIGH-1')).toBeInTheDocument();
    // E
    expect(screen.getByText('Label-MEDIUM-2')).toBeInTheDocument();
    // A
    expect(screen.getByText('Label-LOW-3')).toBeInTheDocument();
    // R (missing)
    expect(screen.getAllByRole('button')[3]).toHaveTextContent('Not Selected');
  });

  it('renders horizontal rules between rows except after the last', () => {
    render(<RiskRatingCell rating={[]} />);
    const hrs = screen.getAllByRole('separator');
    expect(hrs).toHaveLength(3);
  });
});
