import {
  cleanObject,
  parseDate,
  getDateRangeFilters,
  withDefault,
  getErrorMessage,
  getRaApprovalStatus,
  getRiskRatingStatus,
  getRiskRatingValue,
  compareTemplateItems,
  type CategoryItem,
  type HazardItem,
} from '../../src/utils/common';
import {RAStatus, RiskRatingStatus} from '../../src/enums';
import moment from 'moment';

describe('cleanObject', () => {
  it('removes keys with null or undefined values', () => {
    const input = {a: null, b: undefined, c: 'value'};
    const result = cleanObject(input);
    expect(result).toEqual({c: 'value'});
  });

  it('removes keys with empty arrays', () => {
    const input = {a: [], b: [1, 2]};
    const result = cleanObject(input);
    expect(result).toEqual({b: [1, 2]});
  });

  it('removes nested empty objects recursively', () => {
    const input = {
      a: {
        b: {},
        c: {
          d: null,
        },
      },
      e: 'value',
    };
    const result = cleanObject(input);
    expect(result).toEqual({e: 'value'});
  });

  it('keeps nested objects with non-empty keys', () => {
    const input = {
      a: {
        b: 'value',
      },
    };
    const result = cleanObject(input);
    expect(result).toEqual(input);
  });

  it('returns empty object if all values are empty or null', () => {
    const input = {
      a: null,
      b: [],
      c: {
        d: [],
      },
    };
    const result = cleanObject(input);
    expect(result).toEqual({});
  });
});

describe('parseDate', () => {
  it('returns formatted date string for valid date input', () => {
    const date = new Date('2023-01-15T00:00:00Z');
    const formatted = parseDate(date);
    expect(formatted).toBe(moment(date).format('DD MMM YYYY'));
  });

  it('returns undefined for invalid date string', () => {
    const invalidDate = 'not-a-date';
    expect(parseDate(invalidDate)).toBeUndefined();
  });

  it('returns undefined when date is null or undefined', () => {
    expect(parseDate(null)).toBeUndefined();
    expect(parseDate(undefined)).toBeUndefined();
  });

  it('formats date with custom format string', () => {
    const date = '2023-06-17';
    const formatted = parseDate(date, 'YYYY/MM/DD');
    expect(formatted).toBe(moment(date).format('YYYY/MM/DD'));
  });
});

describe('getDateRangeFilters', () => {
  it('returns empty object if no range is provided', () => {
    expect(getDateRangeFilters('date')).toEqual({});
    expect(getDateRangeFilters('date', null)).toEqual({});
    expect(getDateRangeFilters('date', [null, null])).toEqual({});
  });

  it('returns only start_date filter if only start date is provided', () => {
    const result = getDateRangeFilters('created', ['2023-01-01', null]);
    expect(result).toEqual({'created[start_date]': '2023-01-01'});
  });

  it('returns only end_date filter if only end date is provided', () => {
    const result = getDateRangeFilters('updated', [null, '2023-12-31']);
    expect(result).toEqual({'updated[end_date]': '2023-12-31'});
  });

  it('returns both start_date and end_date filters if both provided', () => {
    const result = getDateRangeFilters('date', ['2023-01-01', '2023-12-31']);
    expect(result).toEqual({
      'date[start_date]': '2023-01-01',
      'date[end_date]': '2023-12-31',
    });
  });
});

describe('withDefault', () => {
  it('returns original value if not empty', () => {
    expect(withDefault('Hello')).toBe('Hello');
    expect(withDefault(123)).toBe(123);
    expect(withDefault(true)).toBe(true);
    expect(withDefault('  not empty  ')).toBe('  not empty  ');
  });

  it('returns default placeholder for empty values', () => {
    expect(withDefault(null)).toBe('---');
    expect(withDefault(undefined)).toBe('---');
    expect(withDefault('')).toBe('---');
    expect(withDefault('   ')).toBe('---');
  });

  it('uses custom placeholder when provided', () => {
    expect(withDefault(null, 'N/A')).toBe('N/A');
    expect(withDefault(undefined, 'None')).toBe('None');
    expect(withDefault('', 'Empty')).toBe('Empty');
  });
});

describe('getRaApprovalStatus', () => {
  it('returns correct status and color for approved status', () => {
    const [status, color] = getRaApprovalStatus(RAStatus.APPROVED);
    expect(status).toBe('Approved');
    expect(color).toBe('green');
  });

  it('returns correct status and color for rejected status', () => {
    const [status, color] = getRaApprovalStatus(RAStatus.REJECTED);
    expect(status).toBe('Rejected');
    expect(color).toBe('red');
  });

  it('returns pending status for other statuses', () => {
    const statuses = [RAStatus.DRAFT, RAStatus.PUBLISHED, RAStatus.INACTIVE];
    statuses.forEach(status => {
      const [result, color] = getRaApprovalStatus(status);
      expect(result).toBe('Pending');
      expect(color).toBe('yellow');
    });
  });
});

describe('getRiskRatingStatus', () => {
  it('returns correct status and color for high risk', () => {
    const [status, color] = getRiskRatingStatus(RiskRatingStatus.HIGH);
    expect(status).toBe('High');
    expect(color).toBe('red');
  });

  it('returns correct status and color for medium risk', () => {
    const [status, color] = getRiskRatingStatus(RiskRatingStatus.MEDIUM);
    expect(status).toBe('Medium');
    expect(color).toBe('yellow');
  });

  it('returns correct status and color for low risk', () => {
    const [status, color] = getRiskRatingStatus(RiskRatingStatus.LOW);
    expect(status).toBe('Low');
    expect(color).toBe('green');
  });

  it('returns null values for unknown risk status', () => {
    const [status, color] = getRiskRatingStatus(999 as RiskRatingStatus);
    expect(status).toBeNull();
    expect(color).toBeNull();
  });
});

describe('getRiskRatingValue', () => {
  it('returns correct enum value for valid risk rating strings', () => {
    expect(getRiskRatingValue('HIGH')).toBe(RiskRatingStatus.HIGH);
    expect(getRiskRatingValue('MEDIUM')).toBe(RiskRatingStatus.MEDIUM);
    expect(getRiskRatingValue('LOW')).toBe(RiskRatingStatus.LOW);
  });

  it('handles case-insensitive input', () => {
    expect(getRiskRatingValue('high')).toBe(RiskRatingStatus.HIGH);
    expect(getRiskRatingValue('Medium')).toBe(RiskRatingStatus.MEDIUM);
    expect(getRiskRatingValue('low')).toBe(RiskRatingStatus.LOW);
  });

  it('handles whitespace in input', () => {
    expect(getRiskRatingValue('  HIGH  ')).toBe(RiskRatingStatus.HIGH);
    expect(getRiskRatingValue(' MEDIUM ')).toBe(RiskRatingStatus.MEDIUM);
  });

  it('returns undefined for invalid risk ratings', () => {
    expect(getRiskRatingValue('INVALID')).toBeUndefined();
    expect(getRiskRatingValue('')).toBeUndefined();
    expect(getRiskRatingValue('   ')).toBeUndefined();
  });
});

describe('compareTemplateItems', () => {
  describe('with CategoryItems', () => {
    it('returns true when all items match by ID', () => {
      const source: CategoryItem[] = [
        { id: 1, category: { id: 1, name: 'Category 1' } },
        { id: 2, category: { id: 2, name: 'Category 2' } }
      ];
      const template: CategoryItem[] = [
        { id: 1, category: { id: 1, name: 'Category 1' } },
        { id: 2, category: { id: 2, name: 'Category 2' } }
      ];
      expect(compareTemplateItems(
        source,
        template,
        item => !!item.category_is_other,
        item => item.category?.id
      )).toBe(true);
    });

    it('returns true when other category items match by value', () => {
      const source: CategoryItem[] = [
        { category_is_other: true, value: 'Custom Category' }
      ];
      const template: CategoryItem[] = [
        { category_is_other: true, value: 'Custom Category' }
      ];
      expect(compareTemplateItems(
        source,
        template,
        item => !!item.category_is_other,
        item => item.category?.id
      )).toBe(true);
    });

    it('returns false when lengths are different', () => {
      const source: CategoryItem[] = [{ id: 1, category: { id: 1, name: 'Category 1' } }];
      const template: CategoryItem[] = [
        { id: 1, category: { id: 1, name: 'Category 1' } },
        { id: 2, category: { id: 2, name: 'Category 2' } }
      ];
      expect(compareTemplateItems(
        source,
        template,
        item => !!item.category_is_other,
        item => item.category?.id
      )).toBe(false);
    });

    it('returns false when items do not match', () => {
      const source: CategoryItem[] = [{ id: 1, category: { id: 1, name: 'Category 1' } }];
      const template: CategoryItem[] = [{ id: 2, category: { id: 2, name: 'Category 2' } }];
      expect(compareTemplateItems(
        source,
        template,
        item => !!item.category_is_other,
        item => item.category?.id
      )).toBe(false);
    });
  });

  describe('with HazardItems', () => {
    it('returns true when all hazard items match by ID', () => {
      const source: HazardItem[] = [
        { id: 1, hazard_detail: { id: 1, name: 'Hazard 1' }, hazard_category_is_other: false }
      ];
      const template: HazardItem[] = [
        { id: 1, hazard_detail: { id: 1, name: 'Hazard 1' }, hazard_category_is_other: false }
      ];
      expect(compareTemplateItems(
        source,
        template,
        item => item.hazard_category_is_other,
        item => item.hazard_detail?.id
      )).toBe(true);
    });

    it('returns true when other hazard items match by value', () => {
      const source: HazardItem[] = [
        { hazard_category_is_other: true, value: 'Custom Hazard', hazard_detail: null }
      ];
      const template: HazardItem[] = [
        { hazard_category_is_other: true, value: 'Custom Hazard', hazard_detail: null }
      ];
      expect(compareTemplateItems(
        source,
        template,
        item => item.hazard_category_is_other,
        item => item.hazard_detail?.id
      )).toBe(true);
    });
  });

  it('handles undefined or empty arrays', () => {
    expect(compareTemplateItems(undefined, [], item => true, item => 1)).toBe(false);
    expect(compareTemplateItems([], undefined, item => true, item => 1)).toBe(false);
    expect(compareTemplateItems([], [], item => true, item => 1)).toBe(false);
  });
});
